using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.DataStoreInterface.Dto;

namespace Levelbuild.Domain.StorageEntities.Features.Dto;

public class StorageFile
{
	public string FileId { get; init; }
	public string? ElementId { get; init; }
	public long? RevisionNumber { get; init; }

	[ExcludeFromCodeCoverage]
	public long FileLength { get; set; }

	public string FileName { get; set; }

	[ExcludeFromCodeCoverage]
	public string FileExtension { get; set; }

	public DateTime FileDate { get; set; }

	[ExcludeFromCodeCoverage]
	public string FileHash { get; set; }

	private Stream InternalStream { get; set; }

	[ExcludeFromCodeCoverage]
	public Stream Read()
	{
		return InternalStream;
	}

	public void SetStream(Stream stream)
	{
		InternalStream = stream;
	}

	#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
	public StorageFile(string fileId, string? elementId, long? revisionNumber)
		#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
	{
		FileId = fileId;
		ElementId = elementId;
		RevisionNumber = revisionNumber;
	}

	public DataStoreFileStream ToDto()
	{
		DataStoreFileStream dataStoreFileStream = new DataStoreFileStream(FileName, FileDate, InternalStream, FileLength);
		return dataStoreFileStream;
	}
}