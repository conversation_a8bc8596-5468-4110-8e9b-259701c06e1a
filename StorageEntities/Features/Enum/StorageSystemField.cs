namespace Levelbuild.Domain.StorageEntities.Features.Enum;

public enum StorageSystemField
{
	Id,
	SysCreateDate,
	SysCreateUser,
	SysModifyDate,
	SysModifyUser,
	SysDeleteDate,
	SysIsDeleted,
	SysFileName,
	SysFileType,
	SysFileSize,
	SysFileDate,
	SysFileHash,
	SysFileContent,
	SysIsLink,
	SysIsLinked,
	SysFulltext,
	SysGroups,
	SysPathId,
	SysCurrentRevision,
	SysStoredRevision,
	SysRevisionHash,
	SysFileId,
	SysInactiveDate,
	SysFavourites,

	// Multivalue Lookup
	StorageFieldDefinitionId,
	SysOrderBy,
	SysIsFavourite
}