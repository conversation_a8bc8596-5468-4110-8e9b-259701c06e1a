using Grpc.Core;
using Levelbuild.Core.FrontendDtos.Device;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.User;
using Levelbuild.Core.FrontendDtos.Workflow;
using Levelbuild.Core.SharedDtos.Enums;
using Levelbuild.Core.ZitadelApiInterface;
using Levelbuild.Entities;
using Levelbuild.Entities.Features.Customer;
using Levelbuild.Entities.Features.DataField;
using Levelbuild.Entities.Features.DataSource;
using Levelbuild.Entities.Features.DataStoreConfig;
using Levelbuild.Entities.Features.Device;
using Levelbuild.Entities.Features.Localization;
using Levelbuild.Entities.Features.Module;
using Levelbuild.Entities.Features.Page;
using Levelbuild.Entities.Features.Page.MultiData;
using Levelbuild.Entities.Features.PageView;
using Levelbuild.Entities.Features.PageView.GridView;
using Levelbuild.Entities.Features.PageView.ListView;
using Levelbuild.Entities.Features.User;
using Levelbuild.Entities.Features.Workflow;
using Levelbuild.Entities.Helpers.DataSource;
using Levelbuild.Frontend.WebApp.Features.User.Services;

namespace Levelbuild.Domain.WebAppTests.Setup;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

public static class EntityCreation
{
	public static CultureEntity CreateCulture(CoreDatabaseContext databaseContext, String name)
	{
		var culture = databaseContext.Cultures.FirstOrDefault(culture => culture.Name == name);
		if (culture != null)
			return culture;
		
		culture = new CultureEntity() { Name = name };
		
		databaseContext.Cultures.Add(culture);
		databaseContext.SaveChanges();
		
		return culture;
	}
	
	public static DataStoreConfigEntity CreateDataStore(CoreDatabaseContext databaseContext, Dictionary<string, object> storeOptions,
														Dictionary<string, object> contextOptions, Guid customerId, string name = "ExampleStore")
	{
		var dataStore = databaseContext.DataStoreConfigs.FirstOrDefault(entity => entity.Name == name);
		if (dataStore != null)
			return dataStore;
		
		databaseContext.DataStoreConfigs.Add(new()
		{
			Name = "ExampleStore",
			Enabled = true,
			Type = DataStoreType.Storage,
			Options = storeOptions
		});
		databaseContext.SaveChanges();
		
		dataStore = databaseContext.DataStoreConfigs.First();
		
		databaseContext.ChangeTracker.Clear();
		databaseContext.DataStoreContexts.Add(new()
		{
			Name = "ExampleStore",
			Enabled = true,
			DataStoreId = dataStore.Id,
			CustomerId = customerId,
			Options = contextOptions
		});
		databaseContext.SaveChanges();
		databaseContext.DataStoreContexts.First().CreateContext();
		
		return dataStore;
	}
	
	public static ModuleEntity CreateModule(CoreDatabaseContext databaseContext, Dictionary<string, object> storeOptions,
													Dictionary<string, object> contextOptions, Guid customerId, Guid userId, string name = "ExampleModule")
	{
		var moduleEntity = databaseContext.Modules.FirstOrDefault(entity => entity.Name == name);
		if (moduleEntity != null)
			return moduleEntity;
		
		var dataStore = databaseContext.DataStoreConfigs.FirstOrDefault();
		if (dataStore == null)
		{
			databaseContext.DataStoreConfigs.Add(new DataStoreConfigEntity()
			{
				Name = "ExampleStoreConfig",
				Enabled = true,
				Type = DataStoreType.Storage,
				Options = storeOptions
			});
			databaseContext.SaveChanges();
			databaseContext.ChangeTracker.Clear();
			
			dataStore = databaseContext.DataStoreConfigs.First();
			databaseContext.ChangeTracker.Clear();
			databaseContext.DataStoreContexts.Add(new()
			{
				Name = "ExampleStoreContext",
				Enabled = true,
				DataStoreId = dataStore.Id,
				CustomerId = customerId,
				Options = contextOptions
			});
			databaseContext.SaveChanges();
			databaseContext.DataStoreContexts.First().CreateContext();
		}
		
		moduleEntity = new ModuleEntity()
		{
			DataStoreId = dataStore.Id,
			Name = name,
			Icon = "fa-check-circle",
			Description = "Example Module",
			ResponsibleId = userId
		};
		databaseContext.Modules.Add(moduleEntity);

		databaseContext.SaveChanges();
		databaseContext.ChangeTracker.Clear();
		
		return databaseContext.Modules.First(module => module.Name == name);
	}
	
	public static DataSourceEntity CreateDataSource(CoreDatabaseContext databaseContext, Dictionary<string, object> storeOptions,
													Dictionary<string, object> contextOptions, Guid customerId, string name = "ExampleSource", bool syncSysFields = true)
	{
		var dataSource = databaseContext.DataSources.FirstOrDefault(entity => entity.Name == name);
		if (dataSource != null)
			return dataSource;
		
		var dataStore = databaseContext.DataStoreConfigs.FirstOrDefault();
		if (dataStore == null)
		{
			databaseContext.DataStoreConfigs.Add(new DataStoreConfigEntity()
			{
				Name = "ExampleStoreConfig",
				Enabled = true,
				Type = DataStoreType.Storage,
				Options = storeOptions
			});
			databaseContext.SaveChanges();
			databaseContext.ChangeTracker.Clear();
			
			dataStore = databaseContext.DataStoreConfigs.First();
			databaseContext.ChangeTracker.Clear();
			databaseContext.DataStoreContexts.Add(new()
			{
				Name = "ExampleStoreContext",
				Enabled = true,
				DataStoreId = dataStore.Id,
				CustomerId = customerId,
				Options = contextOptions
			});
			databaseContext.SaveChanges();
			databaseContext.DataStoreContexts.First().CreateContext();
		}
		
		dataSource = new DataSourceEntity()
		{
			DataStoreId = dataStore.Id,
			Name = name,
			Responsible = "UGA",
			Comment = "Example data source",
			StoragePath = ""
		};
		databaseContext.DataSources.Add(dataSource);
		
		dataSource.SetConnection(new StorageDataSourceConnectionHelper(dataSource));
		var storageDataSource = dataSource.CreateDataSource();
		if (syncSysFields)
			dataSource.SyncStorageFields(databaseContext, storageDataSource);
		
		databaseContext.SaveChanges();
		databaseContext.ChangeTracker.Clear();
		
		return databaseContext.DataSources.First(source => source.Name == name);
	}
	
	public static PageEntity CreatePage(CoreDatabaseContext databaseContext, Dictionary<string, object> storeOptions, Dictionary<string, object> contextOptions,
										Guid customerId, string name = "ExamplePage")
	{
		var page = databaseContext.Pages.FirstOrDefault(entity => entity.Name == name);
		if (page != null)
			return page;
		
		var source = CreateDataSource(databaseContext, storeOptions, contextOptions, customerId, syncSysFields: false);
		
		databaseContext.Pages.Add(new PageEntity()
		{
			Name = name,
			DataSourceId = source.Id,
			DataStoreId = source.DataStoreId,
			Description = "Schlumpf!"
		});
		
		databaseContext.SaveChanges();
		
		return databaseContext.Pages.FirstOrDefault(entity => entity.Name == name)!;
	}
	
	public static PageEntity CreatePageMulti(CoreDatabaseContext databaseContext, Dictionary<string, object> storeOptions, Dictionary<string, object> contextOptions,
										Guid customerId, string name = "ExamplePageMulti")
	{
		var page = databaseContext.MultiDataPages.FirstOrDefault(entity => entity.Name == name);
		if (page != null)
			return page;
		
		var source = CreateDataSource(databaseContext, storeOptions, contextOptions, customerId);
		
		databaseContext.MultiDataPages.Add(new MultiDataPageEntity()
		{
			Name = name,
			DataSourceId = source.Id,
			DataStoreId = source.DataStoreId,
			Description = "Schluempfe!",
			Type = PageType.MultiData,
			SingleRecordBehaviour = DataOpeningType.InTable,
		});

		databaseContext.SaveChanges();
		
		return databaseContext.MultiDataPages.FirstOrDefault(entity => entity.Name == name)!;
	}
	
	public static GridViewEntity CreateGridView(CoreDatabaseContext databaseContext, Dictionary<string, object> storeOptions, Dictionary<string, object> contextOptions,
														Guid customerId, string name ="ExampleGridView")
	{
		var singleDataPage = CreatePage(databaseContext, storeOptions, contextOptions, customerId);
		var gridView = databaseContext.GridViews.FirstOrDefault(entity => entity.PageId == singleDataPage.Id);
		if (gridView != null)
			return gridView;
		
		databaseContext.GridViews.Add(new GridViewEntity()
		{
			PageId = singleDataPage.Id,
			Name = name
		});
		
		databaseContext.SaveChanges();
		return databaseContext.GridViews.FirstOrDefault(entity => entity.PageId == singleDataPage.Id)!;
	}
	
	public static GridViewPageEntity CreateGridViewPage(CoreDatabaseContext databaseContext, Dictionary<string, object> storeOptions, Dictionary<string, object> contextOptions,
											 Guid customerId)
	{
		var page = databaseContext.GridViewPages.FirstOrDefault();
		if (page != null)
			return page;
		
		var gridView = CreateGridView(databaseContext, storeOptions, contextOptions, customerId);
		var multiDataPage = CreatePageMulti(databaseContext, storeOptions, contextOptions, customerId);
		
		databaseContext.GridViewPages.Add(new GridViewPageEntity()
		{
			GridViewId = gridView.Id,
			GridViewColumn = 0,
			Position = 0,
			EmbeddedPageId = multiDataPage.Id
		});

		databaseContext.SaveChanges();
		return databaseContext.GridViewPages.FirstOrDefault()!;
	}
	
	public static PageViewEntity CreateListView(CoreDatabaseContext databaseContext, Dictionary<string, object> storeOptions,
												Dictionary<string, object> contextOptions, Guid customerId, string name = "ExampleListView")
	{
		var listView = databaseContext.PageViews.FirstOrDefault(entity => entity.Name == name);
		if (listView != null)
			return listView;
		
		var page = CreatePage(databaseContext, storeOptions, contextOptions, customerId);
		
		databaseContext.ListViews.Add(new ListViewEntity()
		{
			Name = name,
			PageId = page.Id,
			Description = "Sheesh",
		});
		
		databaseContext.SaveChanges();
		
		return databaseContext.PageViews.FirstOrDefault(entity => entity.Name == name)!;
	}
	
	public static DataFieldEntity CreateDataField(CoreDatabaseContext databaseContext, Dictionary<string, object> storeOptions,
												  Dictionary<string, object> contextOptions, Guid customerId, string name = "Data", bool addToStorage = false, string? dataSourceName = "")
	{
		var dataSource = databaseContext.DataSources.FirstOrDefault(source => source.Name == dataSourceName);
		if (dataSource == null)
			dataSource = CreateDataSource(databaseContext, storeOptions, contextOptions, customerId, syncSysFields: false);
		
		var dataField = databaseContext.DataFields.FirstOrDefault(entity => entity.Name == name && entity.DataSourceId == dataSource.Id);
		if (dataField != null)
			return dataField;

		dataField = new DataFieldEntity()
		{
			Name = name,
			DataSourceId = dataSource.Id,
			Type = DataType.String,
			Length = 50,
			FieldType = DataFieldType.DataField,
		};
		databaseContext.DataFields.Add(dataField);
		if(addToStorage)
			dataField.CreateField();
		
		databaseContext.SaveChanges();
		
		return databaseContext.DataFields.FirstOrDefault(entity => entity.Name == name && entity.DataSourceId == dataSource.Id)!;
	}
	
	public static UserEntity CreateUser(CoreDatabaseContext databaseContext, 
										IZitadelApiClientFactory zitadelApiClientFactory, 
										string customerName, 
										string? name = "ExampleUser", 
										bool fixedCulture = false, 
										CultureEntity? culture = null)
	{
		var managementService = zitadelApiClientFactory.GetManagementClient();
		
		var customer = databaseContext.Customers.First(entity => entity.DisplayName == customerName);
		
		var existingUser = databaseContext.Users.FirstOrDefault(config => config.DisplayName == name);
		if (existingUser != null)
		{
			try
			{
				managementService.AddUserToOrganisation(existingUser.RemoteId, customer.RemoteId);
			}
			catch (RpcException)
			{
				// Happens if user is already part of the org. Can be ignored.
			}
			
			return existingUser;
		}
		
		var preferences = new UserPreferencesEntity();
		if (fixedCulture)
		{
			culture ??= databaseContext.Cultures.FirstOrDefault(entity => entity.Name == "en");
			if (culture == null)
			{
				var cultureEntry = databaseContext.Cultures.Add(new CultureEntity()
				{
					Name = "en"
				});
				databaseContext.SaveChanges();
				culture = cultureEntry.Entity;
			}
			
			preferences.CultureOptionType = CultureOptionType.Fixed;
			preferences.CurrentCulture = culture;
			preferences.CurrentCultureId = culture.Id;
		}
		
		var userDto = new UserDto(customer)
		{
			Username = name,
			Email = "<EMAIL>",
			FirstName = "Baby",
			LastName = "Yoda",
			MainCustomerId = customer.Id.ToString(),
			MainCustomer = customer.ToDto(),
			Preferences = preferences.ToDto()
		};
		var remoteId = managementService.AddHumanUser(userDto, customer.RemoteId);
		var user = UserEntity.FromDto(userDto, remoteId);
		databaseContext.Users.Add(user);
		
		databaseContext.SaveChanges();
		
		return databaseContext.Users.FirstOrDefault(config => config.DisplayName == name)!;
	}
	
	public static UserEntity CreateMachineUser(CoreDatabaseContext databaseContext, IZitadelApiClientFactory zitadelApiClientFactory, string customerName, string? name = "ExampleMachineUser")
	{
		var managementService = zitadelApiClientFactory.GetManagementClient();
		
		var customer = databaseContext.Customers.First(entity => entity.DisplayName == customerName);
		
		var existingUser = databaseContext.Users.FirstOrDefault(config => config.DisplayName == name);
		if (existingUser != null)
		{
			managementService.AddUserToOrganisation(existingUser.RemoteId, customer.RemoteId);
			databaseContext.SaveChanges();
			return existingUser;
		}
		
		var userDto = new UserDto(customer)
		{
			Username = name,
			Email = "<EMAIL>",
			FirstName = "R2",
			LastName = "D2",
			MainCustomerId = customer.Id.ToString(),
			MainCustomer = customer.ToDto(),
		};
		var remoteId = managementService.AddMachineUser(userDto, customer.RemoteId);
		var user = UserEntity.FromDto(userDto, remoteId);
		databaseContext.Users.Add(user);
		
		databaseContext.SaveChanges();
		
		return databaseContext.Users.FirstOrDefault(config => config.DisplayName == name)!;
	}
	
	public static CustomerEntity CreateCustomer(CoreDatabaseContext databaseContext, IZitadelApiClientFactory zitadelApiClientFactory, string name = "ExampleCustomer")
	{
		var existingCustomer = databaseContext.Customers.FirstOrDefault(config => config.DisplayName == name);
		if (existingCustomer != null)
			return existingCustomer;
		
		var managementService = zitadelApiClientFactory.GetManagementClient();
		
		var remoteId = managementService.AddOrganisation(name, new[] { "admin", "user" });
		databaseContext.Customers.Add(new CustomerEntity()
		{
			RemoteId = remoteId,
			DisplayName = name
		});
		
		databaseContext.SaveChanges();
		
		return databaseContext.Customers.FirstOrDefault(config => config.DisplayName == name)!;
	}
	
	public static async Task<DeviceEntity> CreateDeviceAsync(CoreDatabaseContext databaseContext, UserManager userManager, string? name = "ExampleDevice", bool? enabled = true, DeviceType? deviceType = DeviceType.ApiClient, DeviceFormat? deviceFormat = DeviceFormat.Phone, Guid? userId = null)
	{
		if (userId == null)
			userId = (await userManager.GetCurrentUserAsync()).Id;
		var deviceDto = new DeviceDto
		{
			DisplayName = name,
			Enabled = enabled,
			Type = deviceType,
			Format = deviceFormat,
			UserId = userId
		};
		
		var entry = await databaseContext.Devices.AddAsync(DeviceEntity.FromDto(deviceDto, null, databaseContext));
		await databaseContext.SaveChangesAsync();
		
		return entry.Entity;
	}

	public static WorkflowEntity CreateWorkflow(CoreDatabaseContext databaseContext, DataSourceEntity dataSource, string? name = "ExampleWorkflow", WorkflowSlot? slot = WorkflowSlot.First)
	{
		var existingWorkflow = databaseContext.Workflows.FirstOrDefault(workflow => workflow.DataSourceId == dataSource.Id && workflow.Name == name);
		if (existingWorkflow != null)
			return existingWorkflow;
		
		var workflowDto = new WorkflowDto()
		{
			DataSourceId = dataSource.Id,
			Name = name,
			Slot = slot,
			Enabled = true
		};

		var statusField = databaseContext.DataFields.FirstOrDefault(field => field.DataSourceId == dataSource.Id && field.Name == workflowDto.StatusFieldName);
		if (statusField == null)
		{
			statusField = new DataFieldEntity()
			{
				DataSourceId = dataSource.Id,
				Name = workflowDto.StatusFieldName,
				FieldType = DataFieldType.DataField,
				Type = DataType.String,
				Mandatory = true,
				AutoGenerated = true
			};
			databaseContext.DataFields.Add(statusField);
			statusField.SetContext(databaseContext);
			statusField.CreateField();
			databaseContext.SaveChanges();
		}
		workflowDto.StatusFieldId = statusField.Id;
		
		var recipientField = databaseContext.DataFields.FirstOrDefault(field => field.DataSourceId == dataSource.Id && field.Name == workflowDto.RecipientsFieldName);
		if (recipientField == null)
		{
			recipientField = new DataFieldEntity()
			{
				DataSourceId = dataSource.Id,
				Name = workflowDto.RecipientsFieldName,
				FieldType = DataFieldType.DataField,
				Type = DataType.String,
				Multi = true,
				Mandatory = true,
				AutoGenerated = true,
				Length = 255
			};
			databaseContext.DataFields.Add(recipientField);
			recipientField.SetContext(databaseContext);
			recipientField.CreateField();
			databaseContext.SaveChanges();
		}
		workflowDto.RecipientsFieldId = recipientField.Id;
		
		var entity = databaseContext.Workflows.Add(WorkflowEntity.FromDto(workflowDto, null, databaseContext));
		databaseContext.SaveChanges();
		databaseContext.ChangeTracker.Clear();
		
		return entity.Entity;
	}

	public static WorkflowNodeEntity CreateWorkflowNode(CoreDatabaseContext databaseContext, WorkflowEntity workflow, string? name = "ExampleWorkflowNode",
												WorkflowNodeState? state = WorkflowNodeState.Start)
	{
		var existingNode = databaseContext.WorkflowNodes.FirstOrDefault(node => node.WorkflowId == workflow.Id && node.Name == name);
		if (existingNode != null)
			return existingNode;

		var entity = databaseContext.WorkflowNodes.Add(new WorkflowNodeEntity()
		{
			WorkflowId = workflow.Id,
			Name = name!,
			State = state!.Value,
			Icon = "bug",
			Sorting = 0
		});
		databaseContext.SaveChanges();
		databaseContext.ChangeTracker.Clear();
		
		return entity.Entity;
	}
}