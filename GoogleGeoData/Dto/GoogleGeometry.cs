using Newtonsoft.Json;

namespace Levelbuild.Domain.GoogleGeoData.Dto;

/// <summary>
/// dto reflecting the geometry object of a Google geo data response
/// </summary>
public class GoogleGeometry
{
	/// <summary>
	/// Area boundaries
	/// </summary>
	public required GoogleBoundary Bounds;
	
	/// <summary>
	/// Location object (Lat/Long)
	/// </summary>
	public required GoogleLatLong Location;

	/// <summary>
	/// Location type
	/// </summary>
	[JsonProperty("location_type")]
	public required string LocationType;
	
	/// <summary>
	/// Viewport
	/// </summary>
	public GoogleBoundary? Viewport;
}