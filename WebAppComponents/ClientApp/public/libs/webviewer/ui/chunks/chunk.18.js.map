{"version": 3, "sources": ["webpack:///./src/ui/node_modules/core-js/modules/es.array-buffer.constructor.js", "webpack:///./src/ui/node_modules/core-js/modules/es.typed-array.int8-array.js", "webpack:///./src/ui/node_modules/core-js/modules/es.typed-array.uint8-clamped-array.js"], "names": ["$", "global", "arrayBufferModule", "setSpecies", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "forced", "createTypedArrayConstructor", "init", "data", "byteOffset", "length", "this"], "mappings": "4FACA,IAAIA,EAAI,EAAQ,IACZC,EAAS,EAAQ,IACjBC,EAAoB,EAAQ,KAC5BC,EAAa,EAAQ,KAGrBC,EAAcF,EAA8B,YAKhDF,EAAE,CAAEC,QAAQ,EAAMI,aAAa,EAAMC,OAJbL,EAAmB,cAIwBG,GAAe,CAChFA,YAAaA,IAGfD,EAVmB,gB,qBCNe,EAAQ,IAI1CI,CAA4B,QAAQ,SAAUC,GAC5C,OAAO,SAAmBC,EAAMC,EAAYC,GAC1C,OAAOH,EAAKI,KAAMH,EAAMC,EAAYC,Q,qBCNN,EAAQ,IAI1CJ,CAA4B,SAAS,SAAUC,GAC7C,OAAO,SAA2BC,EAAMC,EAAYC,GAClD,OAAOH,EAAKI,KAAMH,EAAMC,EAAYC,OAErC", "file": "chunks/chunk.18.js", "sourcesContent": ["'use strict';\nvar $ = require('../internals/export');\nvar global = require('../internals/global');\nvar arrayBufferModule = require('../internals/array-buffer');\nvar setSpecies = require('../internals/set-species');\n\nvar ARRAY_BUFFER = 'ArrayBuffer';\nvar ArrayBuffer = arrayBufferModule[ARRAY_BUFFER];\nvar NativeArrayBuffer = global[ARRAY_BUFFER];\n\n// `ArrayBuffer` constructor\n// https://tc39.es/ecma262/#sec-arraybuffer-constructor\n$({ global: true, constructor: true, forced: NativeArrayBuffer !== ArrayBuffer }, {\n  ArrayBuffer: ArrayBuffer\n});\n\nsetSpecies(ARRAY_BUFFER);\n", "var createTypedArrayConstructor = require('../internals/typed-array-constructor');\n\n// `Int8Array` constructor\n// https://tc39.es/ecma262/#sec-typedarray-objects\ncreateTypedArrayConstructor('Int8', function (init) {\n  return function Int8Array(data, byteOffset, length) {\n    return init(this, data, byteOffset, length);\n  };\n});\n", "var createTypedArrayConstructor = require('../internals/typed-array-constructor');\n\n// `Uint8ClampedArray` constructor\n// https://tc39.es/ecma262/#sec-typedarray-objects\ncreateTypedArrayConstructor('Uint8', function (init) {\n  return function Uint8ClampedArray(data, byteOffset, length) {\n    return init(this, data, byteOffset, length);\n  };\n}, true);\n"], "sourceRoot": ""}