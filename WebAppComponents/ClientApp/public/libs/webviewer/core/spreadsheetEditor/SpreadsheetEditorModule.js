!function(e){var t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(r,o,function(t){return e[t]}.bind(null,o));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="/core/spreadsheetEditor",n(n.s=0)}([function(e,t,n){e.exports=n(1)},function(e,t){self.importScripts("SpreadsheetEditorEngine.js"),LtExcelEngineModule().then((function(e){self.onmessage=function(t){if("initialize"===t.data.action){var n=null;try{var r=t.data.value;if(r){var o=e.lengthBytesUTF8(r)+1;n=e._malloc(o),e.stringToUTF8(r,n,o)}var a=e._L_ExcelEngine_WASM_Initialize(n);self.postMessage({type:t.data.action,message:"success",data:a})}catch(e){self.postMessage({type:t.data.action,errorMessage:"Error Initializing Engine, ".concat(e)})}finally{n&&e._free(n)}}else if("jsonToXlsx"===t.data.action){var s=t.data.value,i=e._malloc(4),l=e.lengthBytesUTF8(s)+1,c=e._malloc(l);e.stringToUTF8(s,c,l);var f=e._L_ExcelEngine_WASM_JsonToXlsx(c,i),u=e.HEAP32[i>>2],d=new Uint8Array(e.HEAPU8.buffer,f,u);self.postMessage({type:"jsonToXlsx",message:d}),e._free(c),e._free(f),e._free(i)}else if("excelToJson"===t.data.action){var g=t.data.value,p=e._malloc(4),_=new Uint8Array(g),y=e._malloc(_.length);e.HEAPU8.set(_,y);var E=e._L_ExcelEngine_WASM_ExcelToJson(y,_.length,p),m=e.HEAP32[p>>2],v=new Uint8Array(e.HEAPU8).subarray(E,E+m),x=new TextDecoder("utf-8").decode(v);e._free(E),e._free(y),e._free(p),self.postMessage({type:"excelToJson",message:x})}},self.postMessage({type:"engineLoaded",message:"LEADTOOLSExcelEngine_Loaded"})})).catch((function(e){self.postMessage({type:"engineLoadedError",message:"Error loading SpreadsheetEditorEngine.wasm, ".concat(e)})}))}]);