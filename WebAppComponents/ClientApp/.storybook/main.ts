import type { StorybookConfig } from '@storybook/web-components-vite'
import { buildComponentsFile } from '@i18n/translation-service.ts'

buildComponentsFile()

const config: StorybookConfig = {
	stories: [ '../src/**/*.mdx', '../src/**/*.stories.@(js|jsx|ts|tsx)' ],
	addons: [ '@storybook/addon-links', '@storybook/addon-essentials' ],
	framework: {
		name: '@storybook/web-components-vite',
		options: {},
	},
	docs: {
		autodocs: 'tag',
	},
	core: {
		disableTelemetry: true, // 👈 Disables telemetry
	},
	staticDirs: [
		'./../public',
		{ from: './assets/', to: '/assets/' },
		{ from: '../public/libs/fontawesome/css', to: '/libs/fontawesome/css/' }, 
		{ from: '../public/libs/fontawesome/webfonts', to: '/libs/fontawesome/webfonts/' } 
	],
}
export default config
