import { css, html, LitElement, PropertyValues } from 'lit'
import { customElement, property, queryAssignedElements, query } from 'lit/decorators.js'
import * as styles from '@/shared/component-styles.ts'
import {  } from 'lit/decorators.js'
import { classMap } from 'lit/directives/class-map.js'

export type SideNavInfoType = {
	label: string,
	skeleton?: boolean
}

/**
 * SideNavInfo web component using LIT (https://lit.dev)
 */
@customElement('lvl-side-nav-info')
export class SideNavInfo extends LitElement implements SideNavInfoType {
	
	static styles = [
		styles.base,
		styles.color,
		styles.animation,
		styles.skeleton,
		css`
			:host {
				display: grid;
				grid-template-columns: subgrid;
				grid-column: span 2;
			}
			
			.nav-info {
				display: grid;
				grid-column: span 2;
				grid-template-columns: subgrid;
				align-items: center;
				column-gap: var(--size-spacing-s, 0.25rem);
			}

			.nav-info.skeleton__block {
				--clr-skeleton-block: var(--cp-clr-background-lvl-2);
			}

			.nav-info.skeleton__block * {
				opacity: 0;
			}
			
			.nav-info__label {
				font-size: var(--size-text-s);
				color: var(--cp-clr-text-secondary);
				grid-column: span 2;
			}

			.nav-info__left {
				white-space: nowrap;
				text-overflow: ellipsis;
				overflow: hidden;
			}
			
			.nav-info__right {
				justify-self: end;
				min-height: var(--size-text-l);
				font-size: 0;
			}

			.nav-info__right::slotted(*) {
				font-size: var(--size-text-m);
			}
			
			slot {
				display: block;
			}

			.nav-info:has(.nav-info__right.empty) .nav-info__left {
				grid-column: span 2;
				min-height: var(--size-text-l);
			}
			
		`,
	]

	//#region attributes

	@property()
	label: string = ""

	@property({ type: Boolean, reflect: true })
	skeleton: boolean = false
	
	//#endregion

	//#region states
	//#endregion states

	//#region private properties

	@query('.nav-info__right')
	private _slotRight!: HTMLElement

	@queryAssignedElements({ slot: 'right' })
	private _infosRight?: HTMLElement[]
	
	//#endregion

	//#region lifecycle callbacks

	protected firstUpdated(_changedProperties: PropertyValues) {
		super.firstUpdated(_changedProperties)
		this.checkSlotContent()
	}

	//#endregion

	// Called when the web component is rendered. Returns the HTML appearance for our web component.
	render() {
		const classes = {
			'nav-info': true,
			skeleton__block: this.skeleton,
		}
		
		return html`
			<div class="${classMap(classes)}">
				<span class="nav-info__label">${this.label}</span>
				<slot class="nav-info__left"></slot>
				<slot class="nav-info__right" name="right" @slotchange=${this.handleSlotChange}></slot>
			</div>
		`
	}

	//#region public methods
	//#endregion

	//#region private methods

	private checkSlotContent(){
		if(!this._infosRight?.length)
			this._slotRight.classList.add('empty')
	}

	private handleSlotChange(_: Event) {
		this.checkSlotContent()
	}
	
	//#endregion
}

// Define a connection between html element tag and LitElement class 
declare global {
	interface HTMLElementTagNameMap {
		'lvl-side-nav-info': SideNavInfo
	}
}