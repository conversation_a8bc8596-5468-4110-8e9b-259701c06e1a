import type { <PERSON><PERSON>, <PERSON><PERSON>bj as Story } from '@storybook/web-components'
import { html, TemplateResult } from 'lit'
import { ifDefined } from 'lit/directives/if-defined.js'
import { LevelStory } from '@story-home/support/commands'
import { MessageType, ToastAttributes } from '@/components/toaster/toast/Toast'
import { ButtonType } from '@/components/atomics/button/Button.ts'
import { ColorState } from '@/enums/color-state.ts'

import('@/components/atomics/button/Button')
import('./ToastMini')

type ToastProperties = Partial<ToastAttributes & {
	buttons: TemplateResult<1>,
	content: string
}>
type ToastStory = Story<ToastProperties>

// Main object that contains the basic configuration for all stories to the web component. 
const meta: Meta = {
	component: 'lvl-toast-mini',
	tags: [ 'autodocs' ],
	render: (_args: ToastProperties) => html`
		<lvl-toast-mini open heading="${ifDefined(_args.heading)}" type="${_args.type}" ?permanent="${_args.permanent}">
			${ifDefined(_args.buttons)}
		</lvl-toast-mini>
	`,
	// add and configure controls in storyboard ui (https://storybook.js.org/docs/react/essentials/controls)
	argTypes: {
		buttons: { table: { disable: true } },
		type: {
			control: 'select',
			options: [ MessageType.Info, MessageType.Success, MessageType.Warning, MessageType.Error ],
			table: {
				type: { summary: 'select' },
				defaultValue: { summary: MessageType.Info },
			},
			description: 'Which type and color scheme shall to message get?',
		},
		heading: {
			control: 'text',
			table: {
				type: { summary: 'text' },
				defaultValue: { summary: '' },
			},
			description: 'Heading of the message. Short and concise.',
		},
		permanent: {
			control: 'boolean',
			description: 'Message closing by button action are automatically?',
			table: {
				type: { summary: 'boolean' },
			},
		},
	},
	includeStories: /^[A-Z]/,
}

export default meta

//#region Stories

/**
 * Appearance of a default ToastMini
 */
export const Default: ToastStory = {
	args: {
		heading: 'Data was stored',
		type: MessageType.Info,
	},
}

/**
 * Appearance of a ToastMini which must be closed manually
 */
export const Permanent: ToastStory = {
	args: {
		heading: 'Message must be closed manually',
		type: MessageType.Info,
		permanent: true,
	},
}

/**
 * Appearance of a default ToastMini with an ok button
 */
export const OkButton: ToastStory = {
	args: {
		heading: 'Unable to connect to your account',
		type: MessageType.Error,
		buttons: html`
			<lvl-button slot="button" type="${ButtonType.Secondary}" color="${ColorState.Info}" label="OK" @click="${() => console.log('OK clicked')}"></lvl-button>
		`,
		permanent: true,
	},
}

/**
 * Appearance of a default ToastMini with close button
 */
export const CloseButton: ToastStory = {
	args: {
		heading: 'You are fired!',
		type: MessageType.Success,
		buttons: html`
			<lvl-button slot="button" type="${ButtonType.Secondary}" color="${ColorState.Info}" label="OK" data-action="close"></lvl-button>
		`,
		permanent: true,
	},
}

//#endregion

// An array that may be imported into cypress tests
export const stories = {
	default: new LevelStory(meta, Default),
	permanent: new LevelStory(meta, Permanent, 'Message with Close Button'),
	ok: new LevelStory(meta, OkButton, 'Ok-Button Message'),
	close: new LevelStory(meta, CloseButton, 'Close-Button Message'),
} as const