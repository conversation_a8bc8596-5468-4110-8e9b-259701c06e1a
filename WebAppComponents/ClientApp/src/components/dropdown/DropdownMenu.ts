import { css, html, LitElement } from 'lit'
import { customElement, property } from 'lit/decorators.js'
import * as styles from '@/shared/component-styles.ts'
import { Size } from '@/enums/size.ts'
import { DropdownMenuItem } from '@/components'

@customElement('lvl-menu')
export class DropdownMenu extends LitElement {

	static styles = [
		styles.base,
		styles.color,
		styles.animation,
		styles.input,
		css`
			:host {
				font-size: 12px;
				color: var(--clr-text, var(--cp-clr-text-primary-positiv));
				background-color: var(--clr-background, var(--cp-clr-background-lvl-0));
				border: 1px solid var(--clr-border, var(--cp-clr-border-medium));
				display: block;
				min-width: var(--min-width, 150px);
				max-width: var(--max-width, unset);
				max-height: var(--max-height, unset);
				padding: var(--size-spacing-m) 0;
				border-radius: var(--size-radius-m);
				overflow: var(--overflow, visible);
				user-select: none;
			}
			
			:host-context(.bottom):host([glue]) {
				border-top-left-radius: 0;
				border-top-right-radius: 0;
			}

			:host-context(.top):host([glue]) {
				border-bottom-left-radius: 0;
				border-bottom-right-radius: 0;
			}

			:host::-webkit-scrollbar-thumb {
				background-color: var(--cp-clr-background-lvl-2);
				border: 0.4rem solid transparent;
				border-radius: var(--size-spacing-m);
				background-clip: padding-box;
				cursor: pointer;
			}

			:host::-webkit-scrollbar-thumb:hover {
				background-color: var(--cp-clr-background-lvl-3);
			}

			:host::-webkit-scrollbar {
				width: 1.2rem;
			}

			:host::-webkit-scrollbar-corner {
				background: transparent;
			}
		`,
	]
	
	//#region attributes
	
	@property({ attribute: 'min-width' })
	minWidth: string = '150px'
	
	@property({attribute: 'max-height'})
	maxHeight?: string = 'auto'

	@property()
	color?: string
	
	@property({attribute: 'background-color'})
	backgroundColor?: string
	
	@property({attribute: 'background-color-hover'})
	backgroundColorHover?: string

	@property({attribute: 'border-color'})
	borderColor?: string
	
	@property({attribute: 'glue'})
	glue?: boolean = false

	@property({ reflect: true })
	size: Size = Size.Small
	
	//#endregion
	
	//#region states
	
	//#endregion

	//#region LIT Functions

	connectedCallback() {
		super.connectedCallback()
		this.addEventListener('click', (event: MouseEvent) => {
			if (event.target == this)
				event.stopPropagation();
		})

		// close open submenu on scroll
		this.addEventListener('scroll', () => {
			this.querySelector('lvl-menu-item[sub-menu-open]')?.removeAttribute('sub-menu-open')
		})
	}

	/**
	 * render component
	 */
	render() {
		return html`
			<style>
				:host {
					${this.minWidth ? `--min-width:${this.minWidth};` : ''}
					${this.maxHeight ? `--max-height:${this.maxHeight};--overflow:auto;` : ''}
					${this.color ? `--clr-text:${this.color};` : ''}
					${this.backgroundColor ? `--clr-background:${this.backgroundColor};` : ''}
					${this.backgroundColorHover ? `--clr-background-hover:${this.backgroundColorHover};` : ''}
					${this.borderColor ? `--clr-border:${this.borderColor};` : ''}
				}
			</style>
			<slot @slotchange="${this.handleSlotchange}"></slot>
		`
	}

	//#endregion

	//#region Event Handlers

	private handleSlotchange() {
		const items = this.querySelectorAll<DropdownMenuItem>('lvl-menu-item')
		items.forEach(item => {
			item.setAttribute('size', this.size)
		})
	}
	
	//#endregion
}

export type DropdownMenuType = {
	'min-width': string,
	'max-height': string,
	'color': string,
	'background-color': string,
	'border-color': string
}

declare global {
	interface HTMLElementTagNameMap {
		'lvl-menu': DropdownMenu
	}
}