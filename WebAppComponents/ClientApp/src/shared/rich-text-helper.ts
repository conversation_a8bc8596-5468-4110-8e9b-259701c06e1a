import { StarterKit } from '@tiptap/starter-kit'
import { Subscript } from '@tiptap/extension-subscript'
import { Superscript } from '@tiptap/extension-superscript'
import { Underline } from '@tiptap/extension-underline'
import { TextAlign } from '@tiptap/extension-text-align'
import Image from '@tiptap/extension-image'
import { Color } from '@tiptap/extension-color'
import { TextStyle } from '@tiptap/extension-text-style'
import { Highlight } from '@tiptap/extension-highlight'
import { Link } from '@tiptap/extension-link'
import { generateHTML, JSONContent } from '@tiptap/core'

export const editorExtensions = [
	StarterKit.configure({
		// Configure an included extension
		heading: {
			levels: [ 1, 2, 3 ],
		},
		horizontalRule: false,
		hardBreak: false,
	}), TextAlign.configure({
		types: [ 'heading', 'paragraph' ],
	}), Image.configure({
		inline: true,
		allowBase64: true,
	}), Link.configure({
		protocols: [ 'mailto' ],
		defaultProtocol: 'https',
		autolink: false,
	}), Highlight.configure({
		multicolor: true,
	}), TextStyle, Color, Superscript, Subscript, Underline,
]

/**
 * Transform JSON output from tiptap to a valid html string
 * @param value JSON Object in a for tiptap readable structure
 */
export function jsonToHtml(value: JSONContent | string) {
	if (!value)
		return ''

	// try to parse -> if it's a regular string then there is nothing to do
	try {
		let content: JSONContent = typeof value === 'string' ? JSON.parse(value) : value
		return generateHTML(content, editorExtensions)
	} catch(e) {
		return value.toString()
	}
}