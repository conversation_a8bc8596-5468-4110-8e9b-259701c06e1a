export class SystemHelper {

	private _thousandSeparator?: string
	private _decimalSeparator?: string
	private _browserLocales?: string[]
	private _browserLocale?: string

	// TODO: time format with/without seconds?
	private _datetimeFormat?: { date: string, time: string }

	static reset() {
		System = new SystemHelper()
	}

	get thousandSeparator(): string {
		if (!this._thousandSeparator) {
			this._thousandSeparator = Number(1000).toLocaleString(this.browserLocale, {
				minimumFractionDigits: 0,
				maximumFractionDigits: 0,
			}).substring(1, 2)
		}

		return this._thousandSeparator
	}

	get decimalSeparator(): string {
		if (!this._decimalSeparator) {
			this._decimalSeparator = Number(1).toLocaleString(this.browserLocale, {
				minimumFractionDigits: 1,
				maximumFractionDigits: 1,
			}).substring(1, 2)
		}

		return this._decimalSeparator
	}

	get browserLocalesShort(): string[] {
		return [
			...new Set(this.browserLocales.map(locale => locale.split(/[-_]/)[0])),
		] // conversion to Set and back to Array assures, that values are unique
	}

	get browserLocaleShort(): string {
		return this.browserLocale.split(/[-_]/)[0]
	}

	get browserLocales(): string[] {
		if (!this._browserLocales)
			this._browserLocales = this.getLocalesFromNavigator()
		return this._browserLocales
	}

	/**
	 * Get the browser preferred language code of the user
	 */
	get browserLocale() {
		if (!this._browserLocale)
			this._browserLocale = this.browserLocales.length > 0 ? this.browserLocales[0] : 'en'
		return this._browserLocale
	}

	get datetimeFormat() {
		if (!this._datetimeFormat)
			this._datetimeFormat = this.getUserDatetimeFormat()
		return this._datetimeFormat
	}

	/**
	 * gets the date + time format for the current user
	 * TODO: set 'format' globally via context not browser locale
	 */
	private getUserDatetimeFormat(): { date: string, time: string } {
		switch (this.browserLocale.toLowerCase()) {
			case 'de':
			case 'de-de':
				return { date: 'dd.MM.yyyy', time: 'HH:mm' }
			case 'en':
			case 'en-gb':
				return { date: 'dd/MM/yyyy', time: 'hh:mm' }
			case 'en-us':
				return { date: 'MM/dd/yyyy', time: 'hh:mm' }
		}

		return { date: 'dd/MM/yyyy', time: 'hh:mm' }
	}

	/**
	 * Get all the browser preferred language codes of the user
	 */
	private getLocalesFromNavigator() {
		let browserLocales = navigator.languages === undefined ? [ navigator.language ] : navigator.languages
		if (!browserLocales)
			return []

		return browserLocales.map(locale => {
			return locale.trim()
		})
	}
}

export let System = new SystemHelper()