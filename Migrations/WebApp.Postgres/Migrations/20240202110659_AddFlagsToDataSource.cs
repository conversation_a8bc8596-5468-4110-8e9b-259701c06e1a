using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Levelbuild.Migrations.Postgres.Migrations
{
    /// <inheritdoc />
    public partial class AddFlagsToDataSource : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "StoreFieldContent",
                table: "DataSources",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "StoreFileContent",
                table: "DataSources",
                type: "boolean",
                nullable: false,
                defaultValue: false);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "StoreFieldContent",
                table: "DataSources");

            migrationBuilder.DropColumn(
                name: "StoreFileContent",
                table: "DataSources");
        }
    }
}
