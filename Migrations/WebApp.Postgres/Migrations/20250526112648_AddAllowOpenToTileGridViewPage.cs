using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Levelbuild.Migrations.Postgres.Migrations
{
    /// <inheritdoc />
    public partial class AddAllowOpenToTileGridViewPage : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Sign",
                table: "GridViewPages");

            migrationBuilder.AddColumn<bool>(
                name: "AllowOpenNewTab",
                table: "GridViewPages",
                type: "boolean",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AllowOpenNewTab",
                table: "GridViewPages");

            migrationBuilder.AddColumn<string>(
                name: "Sign",
                table: "GridViewPages",
                type: "character varying(255)",
                maxLength: 255,
                nullable: true);
        }
    }
}
