using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Levelbuild.Migrations.Postgres.Migrations
{
    /// <inheritdoc />
    public partial class MoveHeaderElementsfromPageEntitytoSingleDataPageEntity : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_PageHeaderElements_Pages_PageId",
                table: "PageHeaderElements");

            migrationBuilder.AddForeignKey(
                name: "FK_PageHeaderElements_SingleDataPage_PageId",
                table: "PageHeaderElements",
                column: "PageId",
                principalTable: "SingleDataPage",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_PageHeaderElements_SingleDataPage_PageId",
                table: "PageHeaderElements");

            migrationBuilder.AddForeign<PERSON>ey(
                name: "FK_PageHeaderElements_Pages_PageId",
                table: "PageHeaderElements",
                column: "PageId",
                principalTable: "Pages",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
