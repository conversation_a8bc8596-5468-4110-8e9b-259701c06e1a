using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Levelbuild.Migrations.Postgres.Migrations
{
    /// <inheritdoc />
    public partial class PageBreadcrumbLabel : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Test",
                table: "ListViews");

            migrationBuilder.RenameColumn(
                name: "Test",
                table: "SingleDataPage",
                newName: "BreadcrumbLabel");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "BreadcrumbLabel",
                table: "SingleDataPage",
                newName: "Test");

            migrationBuilder.AddColumn<string>(
                name: "Test",
                table: "ListViews",
                type: "character varying(255)",
                maxLength: 255,
                nullable: true);
        }
    }
}
