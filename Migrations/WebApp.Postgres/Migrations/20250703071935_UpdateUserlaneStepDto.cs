using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Levelbuild.Migrations.Postgres.Migrations
{
    /// <inheritdoc />
    public partial class UpdateUserlaneStepDto : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "TargetElement",
                table: "UserlaneSteps");

            migrationBuilder.AlterColumn<string>(
                name: "Title",
                table: "UserlaneSteps",
                type: "character varying(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "character varying(255)",
                oldMaxLength: 255,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Description",
                table: "UserlaneSteps",
                type: "character varying(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "character varying(255)",
                oldMaxLength: 255,
                oldNullable: true);

            // Drop the existing ActionType column
            migrationBuilder.DropColumn(
                name: "ActionType",
                table: "UserlaneSteps");

            // Add ActionType as integer
            migrationBuilder.AddColumn<int>(
                name: "ActionType",
                table: "UserlaneSteps",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "Target",
                table: "UserlaneSteps",
                type: "character varying(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "TargetValue",
                table: "UserlaneSteps",
                type: "character varying(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Trigger",
                table: "UserlaneSteps",
                type: "character varying(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Target",
                table: "UserlaneSteps");

            migrationBuilder.DropColumn(
                name: "TargetValue",
                table: "UserlaneSteps");

            migrationBuilder.DropColumn(
                name: "Trigger",
                table: "UserlaneSteps");

            migrationBuilder.AlterColumn<string>(
                name: "Title",
                table: "UserlaneSteps",
                type: "character varying(255)",
                maxLength: 255,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "character varying(255)",
                oldMaxLength: 255);

            migrationBuilder.AlterColumn<string>(
                name: "Description",
                table: "UserlaneSteps",
                type: "character varying(255)",
                maxLength: 255,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "character varying(255)",
                oldMaxLength: 255);

            // Drop the integer ActionType column
            migrationBuilder.DropColumn(
                name: "ActionType",
                table: "UserlaneSteps");

            // Add ActionType as string
            migrationBuilder.AddColumn<string>(
                name: "ActionType",
                table: "UserlaneSteps",
                type: "character varying(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TargetElement",
                table: "UserlaneSteps",
                type: "character varying(255)",
                maxLength: 255,
                nullable: true);
        }
    }
}
