using Levelbuild.Core.FrontendDtos.Userlane;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Entities.Features.User;
using Levelbuild.Entities.Features.Userlane;
using Levelbuild.Entities.Features.UserlaneStepAction;
using Levelbuild.Entities.Features.UserlaneStepTestCondition;
using Levelbuild.Entities.Interfaces;

namespace Levelbuild.Entities.Features.UserlaneStep;

/// <inheritdoc cref="UserlaneStepDto" />
public class UserlaneStepEntity : PersistentEntity<UserlaneStepEntity>, IAdministrationEntity<UserlaneStepEntity, UserlaneStepDto>
{
		
	/// <summary>
	/// 
	/// </summary>
	public required Guid UserlaneId { get; set; }

	/// <summary>
	/// Defines the type of action to be performed in a userlane step.
	/// </summary>
	/// <remarks>
	/// This property specifies the kind of user interaction or operation associated with the step. The action type dictates how the step is executed or presented to the user within the userlane flow.
	/// </remarks>
	public required UserlaneStepActionType ActionType { get; set; }

	/// <summary>
	/// Represents the position of the userlane step in a sequence for a given userlane.
	/// </summary>
	/// <remarks>
	/// The value of this property determines the order in which the steps are presented or executed.
	/// </remarks>
	public required int Order { get; set; }

	/// <summary>
	/// Specifies the trigger event associated with a userlane step.
	/// </summary>
	/// <remarks>
	/// This property identifies the event that triggers the userlane step execution.
	/// </remarks>
	public required string Trigger { get; set; }

	/// <summary>
	/// Specifies the target element associated with a userlane step.
	/// </summary>
	/// <remarks>
	/// This property identifies a specific UI element that the userlane step interacts with or relates to.
	/// The target element determines the context or focus of the step within the userlane process.
	/// </remarks>
	public required string Target { get; set; }

	/// <summary>
	/// Specifies the target value associated with a userlane step.
	/// </summary>
	/// <remarks>
	/// This property defines the value to be used with the target element in the userlane step.
	/// </remarks>
	public required string TargetValue { get; set; }

	/// <summary>
	/// Represents the title of a userlane step.
	/// </summary>
	/// <remarks>
	/// This property specifies the heading or name associated with the step, providing a clear and concise label that helps identify the purpose or context of the step within the userlane flow.
	/// </remarks>
	public required string Title { get; set; }

	/// <summary>
	/// Represents the description of a userlane step.
	/// </summary>
	/// <remarks>
	/// This property provides detailed information about the step's purpose and functionality.
	/// </remarks>
	public required string Description { get; set; }

	/// <summary>
	/// 
	/// </summary>
	public int Delay { get; set; }

	/// <summary>
	/// 
	/// </summary>
	public ICollection<UserlaneStepActionEntity>? Actions { get; init; }
	
	/// <summary>
	/// 
	/// </summary>
	public ICollection<UserlaneStepTestConditionEntity>? TestConditions { get; init; }
	
	/// <summary>
	/// 
	/// </summary>
	public virtual UserlaneEntity? Userlane { get; init; }

	/// <inheritdoc />
	public UserlaneStepEntity()
	{
			
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="excludedProperties"></param>
	/// <param name="handledObjects"></param>
	/// <returns></returns>
	public UserlaneStepDto ToDto(string[]? excludedProperties = null, List<string>? handledObjects = null)
	{
		return new UserlaneStepDto(this, excludedProperties!)
		{
			UserlaneId = UserlaneId,
			Order = Order,
			ActionType = ActionType,
			Trigger = Trigger,
			Target = Target,
			TargetValue = TargetValue,
			Title = Title,
			Description = Description,
			Delay = Delay
		};
	}

	/// <inheritdoc />
	public static UserlaneStepEntity FromDto(UserlaneStepDto entityInfo, UserEntity? createdBy = null, CoreDatabaseContext? context = null)
	{
		return new UserlaneStepEntity
		{
			UserlaneId = entityInfo.UserlaneId,
			ActionType = entityInfo.ActionType,
			Trigger = entityInfo.Trigger,
			Target = entityInfo.Target,
			TargetValue = entityInfo.TargetValue,
			Title = entityInfo.Title,
			Description = entityInfo.Description,
			Delay = entityInfo.Delay,
			Order = entityInfo.Order
		};
	}

	/// <inheritdoc />
	public void UpdatePartial(UserlaneStepDto dto, UserEntity? modifiedBy = null)
	{
		ActionType = dto.ActionType;
		Trigger = dto.Trigger;
		Target = dto.Target;
		TargetValue = dto.TargetValue;
		Title = dto.Title;
		Description = dto.Description;
		Delay = dto.Delay;
		Order = dto.Order;
		UserlaneId = dto.UserlaneId;
	}
}