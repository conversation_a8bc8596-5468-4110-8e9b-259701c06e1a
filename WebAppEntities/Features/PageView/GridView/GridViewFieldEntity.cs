using Levelbuild.Core.FrontendDtos.PageView;
using Levelbuild.Entities.Features.User;
using Levelbuild.Entities.Interfaces;
using System.ComponentModel.DataAnnotations.Schema;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.EntityInterface.Constants;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Extensions;
using Levelbuild.Entities.Features.DataField;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;

namespace Levelbuild.Entities.Features.PageView.GridView;

/// <summary>
/// configuration for a field which is embedded into a GridViewSectionEntity
/// </summary>
public class GridViewFieldEntity : GridViewSectionElementEntity, IAdministrationEntity<GridViewFieldEntity, GridViewFieldDto>
{
	/// <summary>
	/// reference to the DataFieldEntity the field is connected to
	/// </summary>
	public Guid? DataFieldId { get; set; }
	
	/// <summary>
	/// DataFieldEntity the field is connected to
	/// </summary>
	[SetNull]
	[ForeignKey(nameof(DataFieldId))]
	public DataFieldEntity? DataField { get; set; }
	
	/// <summary>
	/// Label which describes the field
	/// </summary>
	public string Label { get; set; } = "";
	
	/// <summary>
	/// defines the type of data field which can be linked to this configuration
	/// </summary>
	public InputDataType DataType { get; set; } = InputDataType.String;
	
	/// <summary>
	/// should this be a field which is mandatory?
	/// </summary>
	public bool Required { get; set; }
	
	/// <summary>
	/// should there be a default value?
	/// </summary>
	public string DefaultValue { get; set; } = "";
	
	/// <summary>
	/// should the field display a placeholder while empty?
	/// </summary>
	public string Placeholder { get; set; } = "";
	
	/// <summary>
	/// should the field contain a help text which gets displayed when the user hovers over the info icon
	/// </summary>
	[Text]
	public string HelpText { get; set; } = "";
	
	/// <summary>
	/// should the input value be displayed in a specific color?
	/// </summary>
	[ShortString]
	public string FontColor { get; set; } = "";
	
	/// <summary>
	/// how should the input value be aligned?
	/// </summary>
	public Alignment TextAlign { get; set; } = Alignment.Left;
	
	/// <summary>
	/// should the field be displayed readonly?
	/// </summary>
	public bool Readonly { get; set; }

	/// <summary>
	/// should the field be hidden inside overviews if it is empty? (which is the default)
	/// </summary>
	public bool HideIfEmpty { get; set; } = true;
	
	private IStringLocalizer? _localizer;
	
	private IStringLocalizer? _typeLocalizer;
	
	/// <inheritdoc />
	public GridViewFieldEntity()
	{
		Type = GridViewSectionElementType.Field;
	}
	
	private GridViewFieldEntity(GridViewFieldDto dto, CoreDatabaseContext? databaseContext) : base(dto)
	{
		SectionId = dto.SectionId!.Value;
		if (dto.DataFieldId != null)
			DataFieldId = dto.DataFieldId == "" ? null : Guid.Parse(dto.DataFieldId);
		Type = GridViewSectionElementType.Field;
		DataType = dto.DataType ?? InputDataType.String;
		if (dto.DefaultValue != null)
			DefaultValue = dto.DefaultValue;
	}
	
	/// <inheritdoc />
	public static GridViewFieldEntity FromDto(GridViewFieldDto entityInfo, UserEntity? createdBy = null, CoreDatabaseContext? context = null)
	{
		return new GridViewFieldEntity(entityInfo, context);
	}
	
	/// <inheritdoc />
	public new GridViewFieldDto ToDto(string[]? excludedProperties = null, List<string>? handledObjects = null)
	{
		_localizer ??= StringLocalizerFactory?.Create("GridViewField", "", true);
		_typeLocalizer ??= StringLocalizerFactory?.Create("DataType", "inputLabel");
		var labelTranslated = _localizer != null ? _localizer[Label] : "";
		var typeTranslated = _typeLocalizer != null ? _typeLocalizer[DataType.ToString()] : "";
		return new GridViewFieldDto(this, excludedProperties, handledObjects)
		{
			DataFieldId = DataFieldId != null ? DataFieldId.ToString() : "",
			DataType = DataType,
			LabelTranslated = Label != "" ? labelTranslated : DataField?.Name ?? typeTranslated,
			PlaceholderTranslated = _localizer != null ? _localizer[Placeholder] : "",
			HelpTextTranslated = _localizer != null ? _localizer[HelpText] : "",
			DataTypeTranslated = typeTranslated,
			IsRichText = DataField is { Type: Core.SharedDtos.Enums.DataType.Text, RichText: true },
			Flag = DataField?.Name ?? typeTranslated
		};
	}
	
	/// <inheritdoc />
	public void UpdatePartial(GridViewFieldDto dto, UserEntity? modifiedBy = null)
	{
		if (dto.SectionId != null)
			SectionId = dto.SectionId.Value;
		if (dto.DataFieldId != null) // null = not part of the json, "" = empty
			DataFieldId = dto.DataFieldId == "" ? null : Guid.Parse(dto.DataFieldId);
		if (dto.RowStart > -1)
			RowStart = dto.RowStart;
		if (dto.RowEnd > -1)
			RowEnd = dto.RowEnd;
		if (dto.ColStart > -1)
			ColStart = dto.ColStart;
		if (dto.ColEnd > -1)
			ColEnd = dto.ColEnd;
		if (dto.Label != null)
			Label = dto.Label;
		if (dto.DataType != null)
			DataType = dto.DataType.Value;
		if (dto.Required != null)
			Required = dto.Required.Value;
		if (dto.DefaultValue != null)
			DefaultValue = dto.DefaultValue;
		if (dto.Placeholder != null)
			Placeholder = dto.Placeholder;
		if (dto.HelpText != null)
			HelpText = dto.HelpText;
		if (dto.FontColor != null)
			FontColor = dto.FontColor;
		if (dto.TextAlign.HasValue)
			TextAlign = dto.TextAlign.Value;
		if (dto.Readonly != null)
			Readonly = dto.Readonly.Value;
		if (dto.HideIfEmpty != null)
			HideIfEmpty = dto.HideIfEmpty.Value;
	}
	
	/// <inheritdoc />
	public override void ConfigureModel(ModelBuilder modelBuilder, DatabaseProvider databaseProvider)
	{
		modelBuilder.Entity<GridViewFieldEntity>().ToTable("GridViewFields");
		
		base.ConfigureModel(modelBuilder, databaseProvider);
	}
}