using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions;
using Microsoft.AspNetCore.Razor.TagHelpers;

namespace Levelbuild.Frontend.WebApp.Shared.TagHelpers;

/// <summary>
/// TagHelper for lvl-button-group
/// </summary>
public class ButtonGroupComponentTagHelper : TagHelper
{
	/// <summary>
	/// Name of the buttongroup (used to grab the value of this group)
	/// </summary>
	public string? Name { get; set; }

	/// <summary>
	/// Currently selected value (if any)
	/// </summary>
	public string? Value { get; set; }

	/// <summary>
	/// If set to true, the group is grayed out and can not be toggled
	/// </summary>
	public bool Readonly { get; set; } = false;

	/// <summary>
	/// Standard TagHelper Process method
	/// </summary>
	/// <param name="context"></param>
	/// <param name="output"></param>
	public override void Process(TagHelperContext context, TagHelperOutput output)
	{
		output.TagName = "lvl-button-group";

		if (Name != null)
			output.Attributes.SetAttribute("name", Name);
		if (Value != null)
			output.Attributes.SetAttribute("value", Value);
		if (Readonly)
			output.Attributes.SetAttribute("readonly", "");

		var extraAttributes = this.GetAttributeList(context);
		foreach (var attribute in extraAttributes)
		{
			output.Attributes.SetAttribute(attribute.Key, attribute.Value);
		}
	}
}