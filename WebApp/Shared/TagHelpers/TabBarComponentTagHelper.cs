using System.Diagnostics.CodeAnalysis;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions;
using Microsoft.AspNetCore.Razor.TagHelpers;

namespace Levelbuild.Frontend.WebApp.Shared.TagHelpers;

/// <summary>
/// TagHelper for lvl-tab-bar
/// </summary>
[ExcludeFromCodeCoverage]
public class TabBarComponentTagHelper : TagHelper
{
	/// <summary>
	/// Mark item as loading
	/// </summary>
	public bool Skeleton { get; set; } = false;

	/// <summary>
	/// Standard TagHelper Process method
	/// </summary>
	/// <param name="context"></param>
	/// <param name="output"></param>
	public override void Process(TagHelperContext context, TagHelperOutput output)
	{
		output.TagName = "lvl-tab-bar";

		if (Skeleton)
			output.Attributes.SetAttribute("skeleton", "");

		var extraAttributes = this.GetAttributeList(context);
		foreach (var attribute in extraAttributes)
		{
			output.Attributes.SetAttribute(attribute.Key, attribute.Value);
		}
	}
}