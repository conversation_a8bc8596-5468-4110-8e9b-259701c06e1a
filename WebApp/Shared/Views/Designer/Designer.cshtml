@using System.Text.Json
@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Core.FrontendDtos.PageView
@using Levelbuild.Core.FrontendDtos.Shared
@using Levelbuild.Frontend.WebApp.Shared.Enums
@model Levelbuild.Frontend.WebApp.Shared.ViewModels.DesignerModel
@inject IExtendedStringLocalizerFactory LocalizerFactory
@addTagHelper *, WebApp

@{
	var localizer = LocalizerFactory.Create("PageView", "designer");
	var scriptLocalizer = LocalizerFactory.Create("ScriptMessages", "");
}
<script type="module" vite-src="~/src/.modules/individual.ts"></script>
<script type="module" defer>
	const pageDesigner = document.querySelector('lvl-page-designer');

	// disable save & delete button
	Page.buttonConfig.saveButton.disabled = true;

	// reactivate save & delete button
	Page.getContentChangeSignal().addEventListener("abort", () => {
		Page.buttonConfig.saveButton.disabled = false;
	})

	// register our general edit panel
	Page.registerEditPanel(document.getElementById('edit-panel'))

	// add click event to abort button and close button
	Page.editSlideOut.addEventListener('dialog-close', (event) => {
		const form = Page.editSlideOut.querySelector('lvl-form')
		if (!form)
			return
		
		if (event.cancelable && form.hasChanges()) {
			// prevent closing of slideout
			event.preventDefault()
			
			// show yesno dialog and ask the user what to do
			const yesNoDialog = document.createElement('lvl-dialog')
			yesNoDialog.preset = 'unsavedChanges'
			yesNoDialog.showOnce() // showOnce = dialog gets appended automatically to the DOM and removes itself after any of the preset buttons was clicked
			
			yesNoDialog.addEventListener('dialog-discard', () => {
				form.updateResetMarkers()
				Page.editSlideOut.open = false
			})
			yesNoDialog.addEventListener('dialog-save', () => {
				Page.editSlideOut.querySelector('lvl-button[data-action=save]').click()
			})
			return
		} else if (typeof pageDesigner.pendingAction == 'function') {
			event.preventDefault()
			pageDesigner.pendingAction()
			return
		}

		const formId = form.getAttribute('id')
		const elementId = form.querySelector('input[name=id]').value

		// Abort Edit
		switch (formId) {
			case 'grid-view-section-form':
			case 'grid-view-page-form':
				const section = document.querySelector(`section[id='${elementId}']`)
				if (section)
					section.classList.remove('selected')
				else {
					// pages may be part of a grid as well
					const gridElement = document.querySelector(`lvl-grid-element[id='${elementId}']`)
					if (gridElement)
						gridElement.selected = false
				}
				break
			default:
				const gridElement = document.querySelector(`lvl-grid-element[id='${elementId}']`)
				if (gridElement)
					gridElement.selected = false
				break
		}
		document.getElementById('content-buttons').classList.remove('hiddenByEditPanel')
	})

	const toaster = document.getElementById('toaster')
	const handleSaveButtonClick = async () => {
		const form = Page.editSlideOut.querySelector(' lvl-form')
		const formId = form.getAttribute('id')
		const elementId = form.querySelector('input[name=id]').value

		// Check required inputs
		if (form.hasEmptyRequiredElements())
			return toaster.notifySimple({ heading: '@(scriptLocalizer["FillRequiredFields"])', type: 'error' })

		// check for errors
		if (form.hasErrors())
			return toaster.notifySimple({ heading: '@(scriptLocalizer["CheckHighlightedFields"])', type: 'error' })

		// do we update a column or a grid element?
		if (formId === 'grid-view-section-form') {
			Page.updateEntry(`/Api/GridViewSections/${elementId}`, form.getValues()).then((response) => {
				const section = document.querySelector(`section[id='${elementId}']`)
				pageDesigner.refreshColumnEntity(section, response.data)
				form.updateResetMarkers()
				Page.editSlideOut.open = false
			})
		} else if (formId === 'grid-view-page-form') {
			Page.updateEntry(`/Api/GridViewPages/${elementId}`, form.getValues()).then((response) => {
				const section = document.querySelector(`section[id='${elementId}']`)
				if (section)
					pageDesigner.refreshColumnEntity(section, response.data)
				else {
					const gridElement = document.querySelector(`lvl-grid-element[id='${elementId}']`)
					pageDesigner.refreshGridEntity(gridElement, response.data)
				}
				form.updateResetMarkers()
				Page.editSlideOut.open = false
			})
		} else {
			const gridElement = document.querySelector(`lvl-grid-element[id='${elementId}']`)
			Page.updateEntry(`/Api/${gridElement.config.entityName}s/${elementId}`, form.getValues()).then((response) => {
				pageDesigner.refreshGridEntity(gridElement, response.data)
				form.updateResetMarkers()
				Page.editSlideOut.open = false
			})
		}
	}

	// trigger save
	Page.editSlideOut.querySelector('[data-action="save"]')?.addEventListener('click', handleSaveButtonClick, { signal: Page.getContentChangeSignal() })

</script>
@if (Program.IsDevelopment && !Program.IsRunningInContainer)
{
	<link vite-href="/css/features/single-data-page/page-designer.css" rel="stylesheet" type="text/css"/>
}
else
{
	<link href="@Url.Content("~/css/features/single-data-page/page-designer.css")" rel="stylesheet" type="text/css" asp-append-version="true"/>	
}
@{
	var columnMinWidths = new Dictionary<int, int?>();
	columnMinWidths[1] = Model.GridView.ColumnOneMinWidth;
	columnMinWidths[2] = Model.GridView.ColumnTwoMinWidth;
	columnMinWidths[3] = Model.GridView.ColumnThreeMinWidth;

	var columnRatios = new Dictionary<int, int?>();
	columnRatios[1] = Model.GridView.ColumnOneRatio;
	columnRatios[2] = Model.GridView.ColumnTwoRatio;
	columnRatios[3] = Model.GridView.ColumnThreeRatio;
}
<lvl-page-designer columns="@(Model.GridView.ColumnCount)" page-view-id="@(Model.GridView.Id)" allow-embedded-pages="@(Model.PageType == PageType.SingleData)"
                   column-options='{"columnMinWidths": @(JsonSerializer.Serialize(columnMinWidths)), "columnRatios": @(JsonSerializer.Serialize(columnRatios))}'
                   data-source-type="@(Model.GridView.Page?.DataSource?.Type?.ToString().ToLower())">
	@{
		Dictionary<int, List<ISortableDto>> elementsByColumn = new ();
		for (var i = 0; i < 3; i++)
		{
			elementsByColumn[i] = Model.GridView.Sections.Where(section => section.GridViewColumn == i)
			.ToList<ISortableDto>();

			elementsByColumn[i].AddRange(Model.GridView.Pages.Where(page => page.GridViewColumn == i && page.SectionId == null)
			.ToList<ISortableDto>());
		}
	}
	@for (var i = 0; i < Model.GridView.ColumnCount; i++)
	{
		@foreach (var element in elementsByColumn[i].OrderBy(element => element.Position))
		{
			@if (element is GridViewSectionDto)
			{
				<partial name="_Section.cshtml" model="element"/>
			}
			else if (element is GridViewPageDto)
			{
				<partial name="_PageSection.cshtml" model="element"/>
			}
		}
	}
</lvl-page-designer>
<slide-out-component id="edit-panel" class="side-panel" position="@(Alignment.Right)" heading="@(localizer["editItem"])" anchor icon="pen-to-square" width="450"
                     open="@(ViewData["targetAction"]?.ToString() == "Detail")">
	<div class="content vanishing-scrollbar static-scrollbar"></div>
	<button-component slot="button-left" data-action="cancel" label="@localizer["abortButton"]" type="ButtonType.Secondary" color="ColorState.Info">
	</button-component>
	<button-component slot="button-right" data-action="save" label="@localizer["saveButton"]" type="ButtonType.Primary">
	</button-component>
</slide-out-component>