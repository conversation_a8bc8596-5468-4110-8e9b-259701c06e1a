@using System.Text.Json
@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.DataStoreConfig
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Frontend.WebApp.Shared
@using Levelbuild.Frontend.WebApp.Shared.ControllerDtos
@using Levelbuild.Frontend.WebApp.Shared.Enums
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@using Levelbuild.Frontend.WebApp.Shared.ViewModels
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.AdminListPage
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.BasicMenu
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.CreatePanel
@model Levelbuild.Frontend.WebApp.Features.Module.ViewModels.ModuleList
@inject IExtendedStringLocalizerFactory LocalizerFactory
@addTagHelper *, WebApp
@{
	var localizer = LocalizerFactory.Create("Module", "");
	var storeSlug = ViewContext.RouteData.Values["dataStoreSlug"]?.ToString();
	DataStoreConfigDto? selectedDataStore = Model.DataStores.FirstOrDefault(dataStoreConfig => dataStoreConfig.Slug == storeSlug);
	IList<BaseColumnComponentTagHelper> columns =
	[
		new ListColumnComponentTagHelper() { Type = ListColumnType.Flag, Name="icon", WithConverter = true },
		new ListDataColumnComponentTagHelper() { Name = "name", Label = localizer["list/name"], HideLabel = true },
		new ListDataColumnComponentTagHelper() { Name = "sourcesCount", Label = localizer["list/sourceCount"], TextAlign = Alignment.Right},
	];
	List<QueryParamSortingDto> sortings = [new QueryParamSortingDto() { OrderColumn = "Name", Direction = SortDirection.Asc }];
	List<MenuInfo> menuInfos = [MenuInfo.GetLinkInstance("info-backend-link", "dataStore", selectedDataStore?.Name ?? "", $"/Admin/DataStores/{selectedDataStore?.Slug}")];
	List<MenuItem> menuItems = Model.DataStores.Select(dataStore => new MenuItem(dataStore.Name ?? "", dataStore.Id!.Value.ToString(), null, dataStore.Slug == storeSlug, dataStore.Enabled, dataStore)).ToList();
}
<script type="module" defer>
	function getModuleIcon(data) {
		let icon = ''
		if (data.icon)
			icon = data.icon
	
		return `<i class="fa-light fa-${icon}"></i>`
	}
	
	// Save data needs to be done first!
	@if (selectedDataStore != null)
	{
		@:Page.saveFormData(@(Html.Raw(JsonSerializer.Serialize(selectedDataStore, ConfigHelper.DefaultJsonOptions))))
		@:Page.setMainPage('/Admin/DataStores/@(selectedDataStore.Slug)/Modules')
		@:Page.setBreadcrumbs([{ label: '@localizer["list/pageTitle"]', url: Page.getMainPageUrl() }])
	}

	const renderFlagIcon = (data) => getModuleIcon(data)
	const defaultSortingEnumeration = document.getElementById('module-list')
	defaultSortingEnumeration.querySelector('lvl-list-column[name="icon"]').converter = renderFlagIcon
	

</script>
<vc:basic-menu type="@BasicMenuType.ListFilter" entity="Module" base-route="Admin/DataStores/-/Modules" route-name="Modules" menu-items="@menuItems" menu-infos="@menuInfos" show-state="true"></vc:basic-menu>
<vc:admin-list-page entity="Module" route-name="Modules" columns="@columns" sorting="@sortings" localizer="@localizer" filter-by-menu="true" parent-property-name="dataStoreId" route-params="{dataStoreId:'dataStoreId'}" open-on-row-click="true"></vc:admin-list-page>
<vc:create-panel entity="Module" route-name="Modules" localizer="@localizer" parent-property-name="dataStoreId"></vc:create-panel>
<script type="module" defer>
	// link button in the info part of the menu has to open the selected data store config
	document.querySelector('#info-backend-link .menu-info__link')?.addEventListener('click', () => {
		window.open(`/Admin/DataStores/${Page.getFormData()?.slug ?? ''}`, '_blank')
	})

	const menu = document.getElementById('module-menu');
	@if (selectedDataStore == null)
	{
		<text>
	// no backends available
	if(Page.getFormData()?.name == null) {
		document.querySelector('[data-action=add]').disabled = true
		const infoBackendLabel = document.querySelector('#info-backend-link span')
		if (infoBackendLabel)
			infoBackendLabel.textContent = '@localizer["menu/info/noBackend"]'
		const infoLinkButton = document.querySelector('#info-backend-link .menu-info__link')
		infoLinkButton.classList.add('hide')
		menu?.querySelector('.menu__no-data lvl-button')?.addEventListener('click', () => window.open('/Admin/DataStores', '_blank'))
	} else {
		setBackend()
	}
	</text>
	}
	menu?.addEventListener('nav-item:click', async () => setBackend())

	document.getElementById('module-list')?.addEventListener('list-row:click', async customEvent => {
		if (customEvent?.detail == null || customEvent.detail.rowData == null)
			return

		const rowData = customEvent.detail.rowData
		const menu = document.getElementById("module-menu")
		await Component.waitForComponentInitialization('lvl-side-nav')
		const dataStoreSlug = Page.getValueByPath(rowData, "module.dataStore.slug")

		// Backend
		menu.setInfoData(rowData, "info-dataStore-link-left", "module.dataStore.name")
		menu.setInfoUrl("info-dataStore-link-right", `/Admin/DataStores/${dataStoreSlug}`)
	})

	function setBackend() {
		const backendLabel = document.querySelector('#info-backend-link span')
		if (backendLabel)
			backendLabel.textContent = Page.getFormData()?.name

		const linkButton = document.querySelector('#info-backend-link .menu-info__link')
		if (linkButton){
			linkButton.dataset['href'] = `/Admin/DataStores/${Page.getFormData()?.slug ?? ''}`
			linkButton.classList.remove('hide')
		}
	}
</script>