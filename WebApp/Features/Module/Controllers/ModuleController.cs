using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.Module;
using Levelbuild.Entities;
using Levelbuild.Entities.Features.DataStoreConfig;
using Levelbuild.Entities.Features.Module;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Module.ViewModels;
using Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Features.ViewManagement;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Extensions;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Levelbuild.Frontend.WebApp.Features.Module.Controllers;

public class ModuleController : AdminController<ModuleDto>
{
	/// <inheritdoc />
	public ModuleController(ILogManager logManager, IDbContextFactory<CoreDatabaseContext> contextFactory, UserManager userManager,
							IExtendedStringLocalizerFactory localizerFactory, IVersionReader versionReader) :
		base(logManager, logManager.GetLoggerForClass<ModuleController>(), contextFactory, userManager, localizerFactory, versionReader)
	{
	}

	#region Views

	/// <summary>
	/// Renders the list view with help of the list dto
	/// </summary>
	/// <returns>rendered list view</returns>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Admin/Modules/")]
	[HttpGet("/Admin/DataStores/{dataStoreSlug?}/Modules/")]
	public IActionResult List(string? dataStoreSlug)
	{
		return CachedPartial() ?? RenderPartial(new ModuleList()
		{
			DataStores = DatabaseContext.DataStoreConfigs.AsQueryable().OrderBy(store => store.Name).ToDtoList().ToList()
		});
	}

	/// <summary>
	/// Renders the detail view with help of the module dto
	/// </summary>
	/// <param name="dataStoreSlug">readable identifier for a specific data store</param>
	/// <param name="slug">readable identifier for a specific module</param>
	/// <returns>rendered detail view</returns>
	/// <exception cref="ElementNotFoundException">slug is not existing</exception>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Admin/Modules/Edit/{menu?}")]
	[HttpGet("/Admin/DataStores/{dataStoreSlug?}/Modules/{slug}/{menu?}")]
	public IActionResult Detail(string? dataStoreSlug, string? slug)
	{
		if (string.IsNullOrEmpty(dataStoreSlug) || string.IsNullOrEmpty(slug))
		{
			return CachedPartial() ?? RenderPartial(new ModuleForm(ViewType.Edit));
		}

		DataStoreConfigEntity? dataStore = DatabaseContext.DataStoreConfigs
			.FirstOrDefault(dataStore => dataStore.Slug == dataStoreSlug.ToLower());

		if (dataStore == null)
			throw new ElementNotFoundException($"DataStore configuration with slug: {dataStoreSlug} could not be found");

		var module = DatabaseContext.Modules
			.Include(config => config.DataStore)
			.Include(config => config.DataSources)
			.Include(config => config.Responsible)
			.FirstOrDefault(config => config.DataStoreId == dataStore.Id && config.Slug == slug.ToLower());

		if (module == null)
			throw new ElementNotFoundException($"Module configuration with slug: {slug} could not be found");

		return RenderPartial(new ModuleForm(ViewType.Edit)
		{
			Module = module.ToDto()
		});
	}
	
	/// <summary>
	/// Render empty create view
	/// </summary>
	/// <returns></returns>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Admin/Modules/Create")]
	[HttpGet("/Admin/DataStores/{dataStoreSlug?}/Modules/Create")]
	public IActionResult Create(string? dataStoreSlug)
	{
		if (string.IsNullOrEmpty(dataStoreSlug))
		{
			return CachedPartial() ?? RenderPartial(new ModuleForm() { Module = new ModuleDto() }, "List", new ModuleList());
		}

		var dataStoreId = DatabaseContext.DataStoreConfigs.FirstOrDefault(config => config.Slug == dataStoreSlug)?.Id;
		if (!dataStoreId.HasValue)
			throw new ElementNotFoundException($"DataStore configuration with slug: {dataStoreSlug} could not be found");

		return CachedPartial() ?? RenderPartial(new ModuleForm() { Module = new ModuleDto() { DataStoreId = dataStoreId.Value } }, "List",
												new ModuleList()
												{
													DataStores = DatabaseContext.DataStoreConfigs.AsQueryable().ToDtoList().ToList()
												});
	}

	#endregion

	#region Actions

	/// <inheritdoc />
	[HttpGet("/Api/Modules/")]
	public override ActionResult<FrontendResponse> Query(QueryParamsDto parameters)
	{
		var modules = DatabaseContext.Modules
			.Include(module => module.DataSources);
		return HandleQueryRequest<ModuleEntity, ModuleDto>(modules, parameters);
	}

	/// <inheritdoc />
	[HttpGet("/Api/Modules/{id:guid}")]
	public override ActionResult<FrontendResponse> Get(Guid id)
	{
		var modules = DatabaseContext.Modules
			.Include(module => module.DataSources)
			.Include(module => module.DataStore)
			.Include(module => module.Responsible);
		return HandleGetRequest<ModuleEntity, ModuleDto>(modules, id);
	}

	/// <inheritdoc />
	[HttpPost("/Api/Modules/")]
	public override async Task<ActionResult<FrontendResponse>> Create([FromBody] ModuleDto configurationDto)
	{
		return await HandleCreateRequestAsync(DatabaseContext.Modules, configurationDto);
	}

	/// <inheritdoc />
	[HttpPatch("/Api/Modules/{id:guid}")]
	public override async Task<ActionResult<FrontendResponse>> Update(Guid id, [FromBody] ModuleDto configurationDto)
	{
		return await HandleUpdateRequestAsync(DatabaseContext.Modules, id, configurationDto);
	}

	/// <inheritdoc />
	[HttpDelete("/Api/Modules/{id:guid}")]
	public override ActionResult<FrontendResponse> Delete(Guid id)
	{
		return HandleDeleteRequest(DatabaseContext.Modules, id);
	}

	#endregion
}