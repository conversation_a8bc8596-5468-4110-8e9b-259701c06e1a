@using Levelbuild.Frontend.WebApp.Shared.Services
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@model Levelbuild.Frontend.WebApp.Features.DataStoreConfig.ViewModels.DataStoreConfigForm
@inject IAssetService AssetService

<!--suppress CssUnusedSymbol -->
<style>
	#data-store-options .icon.loading {
		grid-column: span 2;
		width: fit-content;
		justify-self: center;
	}
	
	#data-store-options {
		display: contents;
	}
</style>
<div class="grid--centered">
	<form-component id="data-store-config-form" class="form">
		<div class="form__options"></div>
	</form-component>
</div>
<template id="data-store-group-template">
	<config-section label="default"></config-section>
</template>
<template id="data-store-option-input-template">
	<div class="form__item">
		<config-label class="option__label" description="" label="" target=""></config-label>
		<input-component class="option__input item__value"></input-component>
	</div>
</template>
<template id="data-store-option-textarea-template">
	<div class="form__item">
		<config-label class="option__label" description="" label="" target=""></config-label>
		<textarea-component class="option__input item__value"></textarea-component>
	</div>
</template>
<template id="data-store-option-autocomplete-template">
	<div class="form__item">
		<config-label class="option__label" description="" label="" target=""></config-label>
		<autocomplete-component class="option__input item__value"></autocomplete-component>
	</div>
</template>
<template id="data-store-option-toggle-template">
	<div class="form__item">
		<config-label class="option__label" description="" label="" target=""></config-label>
		<toggle-component class="option__input item__value"></toggle-component>
	</div>
</template>
<script type="module" defer>
	import { appendConfigurationOptionsToForm } from '@AssetService.SolvePath("/data-store-config/configuration.ts")' 

	const groupTemplate = document.getElementById('data-store-group-template')
	const form = document.getElementById('data-store-config-form')

	@if (Model.DataStoreInfo != null)
	{
		@:appendConfigurationOptionsToForm(Page, @Json.Serialize(Model.DataStoreInfo), @Json.Serialize(Model.DataStoreConfig?.Options));
	}
</script>