@model Levelbuild.Frontend.WebApp.Features.Userlane.ViewModels.UserlaneForm
@using System.Text.Json
@using Levelbuild.Core.EntityInterface
@using Levelbuild.Frontend.WebApp.Shared.Enums
@using Levelbuild.Core.FrontendDtos.Enums
@inject IExtendedStringLocalizerFactory LocalizerFactory
@addTagHelper *, WebApp
@{
	var localizer = LocalizerFactory.Create("UserlaneStep", "");
}

<style>

    :root {
        --visualization-bg-0: var(--clr-white);
        --visualization-bg-1: var(--clr-black-100);
        --visualization-bg-2: var(--clr-black-200);
        --visualization-bg-3: var(--clr-black-300);
        --visualization-bg-4: var(--clr-black-400);
        --visualization-bg-5: var(--clr-black-500);

        --visualization-hover-0: var(--clr-black-300);

        --visualization-blue-0: var(--clr-blue-600);
        --visualization-blue-1: var(--clr-blue-500);
        --visualization-blue-2: var(--clr-blue-400);

        --visualization-txt-0: var(--clr-black);
        --visualization-txt-1: var(--clr-black-950);
        --visualization-txt-2: var(--clr-black-900);
        --visualization-txt-3: var(--clr-black-800);
        --visualization-txt-4: var(--clr-black-700);
        --visualization-txt-5: var(--clr-black-600);
        --visualization-txt-6: var(--clr-black-500);
        --visualization-txt-7: var(--clr-black-400);
        --visualization-txt-8: var(--clr-black-300);
        --visualization-txt-9: var(--clr-black-200);
        --visualization-txt-10: var(--clr-black-100);
        --visualization-txt-11: var(--clr-black-50);

        --visualization-space: var(--clr-black-50);
        --visualization-dots: var(--clr-black-200);
    }

    :root[data-scheme="dark"] {
        --visualization-bg-0: var(--clr-black-100);
        --visualization-bg-1: var(--clr-black-200);
        --visualization-bg-2: var(--clr-black-300);
        --visualization-bg-3: var(--clr-black-400);
        --visualization-bg-4: var(--clr-black-400);
        --visualization-bg-5: var(--clr-black-500);

        --visualization-hover-0: var(--clr-black-300);

        --visualization-blue-0: var(--clr-blue-600);
        --visualization-blue-1: var(--clr-blue-500);
        --visualization-blue-2: var(--clr-blue-400);

        --visualization-space: var(--clr-black-900);
        --visualization-dots: var(--clr-black-300);

        --visualization-txt-0: var(--clr-black);
        --visualization-txt-1: var(--clr-black-950);
        --visualization-txt-2: var(--clr-black-900);
        --visualization-txt-3: var(--clr-black-800);
        --visualization-txt-4: var(--clr-black-700);
        --visualization-txt-5: var(--clr-black-600);
        --visualization-txt-6: var(--clr-black-500);
        --visualization-txt-7: var(--clr-black-400);
        --visualization-txt-8: var(--clr-black-300);
        --visualization-txt-9: var(--clr-black-200);
        --visualization-txt-10: var(--clr-black-100);
        --visualization-txt-11: var(--clr-black-50);
    }


    .visualization-content {
        flex: 1;
        display: grid;
        border-radius: var(--size-radius-l);
        gap: var(--size-spacing-m);
        padding: var(--size-spacing-l);
        margin: var(--size-spacing-l);
        background-color: var(--visualization-space);
    }

    .userlane-chart-container {
        width: 100%;
        height: 100%;
        min-height: 40rem;
        position: relative;
        background-image: radial-gradient(circle at var(--size-spacing-xs) var(--size-spacing-xs),
        var(--visualization-dots) var(--size-spacing-xs),
        transparent 0
        );
        background-size: var(--size-spacing-xl) var(--size-spacing-xl);
        overflow: hidden;
        cursor: grab;
    }

    .userlane-chart-container.dragging {
        cursor: grabbing;
    }

    /* Zoom controls */
    .zoom-controls {
        position: absolute;
        bottom: 20px;
        right: 20px;
        display: flex;
        align-items: center;
        gap: 4px;
        background: var(--visualization-bg-0);
        border: 1px solid var(--visualization-bg-2);
        border-radius: 4px;
        box-shadow: 0 0 0.8rem rgba(0,0,0,0.1);
        z-index: 100;
        font-family: inherit;
        padding: 4px;
    }

    .zoom-button {
        height: 24px;
        border: none;
        background: transparent;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: var(--size-text-m);
        color: var(--visualization-txt-0);
        border-radius: 4px;
        padding: 0 6px;
        white-space: nowrap;
    }

    .zoom-button:hover {
        background: var(--visualization-bg-1);
    }

    .zoom-display {
        padding: 0 8px;
        font-size: var(--size-text-m);
        color: var(--visualization-txt-0);
        min-width: 42px;
        text-align: center;
    }

    /* Zoomable content wrapper */
    .zoomable-content {
        transform-origin: 0 0;
        transition: transform 0.1s ease-out;
    }

    /* Hide FAB button */
    fab-component,
    lvl-fab {
        display: none !important;
        visibility: hidden !important;
    }

    .userlane-step-visualization-page {
        display: flex;
        flex-direction: row;
        flex-grow: 1;
        padding: 0 var(--size-spacing-s);
        overflow: hidden;
    }

    /* ─── Timeline layout ─────────────────────────────────── */
    .ul-timeline{
        display:flex;
        gap:var(--size-spacing-xl);
        padding:var(--size-spacing-m);
        position:absolute;
        top:0;
        left:50%;
        transform:translateX(-50%);
        width:auto;
        min-width:700px;
        max-width:none;
    }

    /* Column headers */
    .ul-column-header{
        font-size:2rem;
        font-weight:700;
        color:var(--visualization-txt-5);
        text-transform:uppercase;
        letter-spacing:0.05em;
        margin-bottom:var(--size-spacing-s);
        text-align:center;
    }

    .ul-actions, .ul-conditions{
        display:flex;
        flex-direction:column;
    }

    .ul-actions{
        position:relative;
        padding-left:calc(var(--size-spacing-xl) + var(--size-spacing-m));
        flex:0 0 auto;
        min-width:350px;
    }

    .ul-conditions{
        flex:1;
        min-width:0;
    }

    /* ─── Condition rows (horizontal layout) ────────────────── */
    .ul-condition-row{
        display:flex;
        align-items:center;
        gap:var(--size-spacing-s);
        margin-bottom:calc(var(--size-spacing-l) * 3);
        height:50px;
        flex-wrap:nowrap;
        overflow:visible;
    }

    /* vertical guide line + dots */
    .ul-actions::before{
        content:"";
        position:absolute;
        left:calc(-1 * var(--size-spacing-xl) - var(--size-spacing-m) + 1.25rem);
        top:3rem;
        bottom:2rem;  /* leaves room for "+ Add Action" btn */
        width:2px;
        z-index:5;
        display:none;
    }

    /* SVG Timeline container */
    .ul-timeline-svg{
        position:absolute;
        left:calc(-1 * var(--size-spacing-xl) - var(--size-spacing-m));
        top:3rem;
        bottom:2rem;
        width:3rem;
        height:auto;
        z-index:2;
        pointer-events:auto;
    }

    /* ─── Action cards ────────────────────────────────────── */
    .ul-card-wrapper{
        margin-bottom:calc(var(--size-spacing-l) * 3);
        margin-left:var(--size-spacing-m);
        position:relative;
        flex-shrink:0;
        width:240px;
        height:52px;
        transition:margin-bottom 0.3s ease;
    }

    .ul-card{
        background:var(--visualization-bg-0);
        border:3px solid #007acc;
        border-left:7px solid #007acc;
        border-radius:5px;
        padding:var(--size-spacing-s) var(--size-spacing-m);
        font-weight:600;
        color:var(--visualization-txt-0);
        box-shadow:0 2px 4px rgb(0 0 0 / .1);
        position:relative;
        display:flex;
        justify-content:space-between;
        align-items:center;
        width:100%;
        height:100%;
    }

    /* Card content area - prevents text overflow */
    .ul-card-content{
        flex:1;
        min-width:0;
        overflow:hidden;
    }

    /* Step circles with cursor icons */
    .ul-step-number{
        position:absolute;
        left:calc(-1 * var(--size-spacing-xl) - var(--size-spacing-m));
        top:50%;
        transform:translateY(-50%);
        width:2.5rem;
        height:2.5rem;
        border-radius:50%;
        display:flex;
        align-items:center;
        justify-content:center;
        z-index:4;
        background:white;
        border:3px solid #d1d5db;
        box-shadow:0 2px 6px rgba(0,0,0,0.1);
    }
    
    /* Create inner circle effect to show line passing through */
    .ul-step-number::before{
        content:"";
        position:absolute;
        left:50%;
        top:50%;
        transform:translate(-50%, -50%);
        width:4px;
        height:100%;
        background:#d1d5db;
        z-index:-1;
    }

    .ul-step-number svg{
        width:1rem;
        height:1rem;
        fill:#6b7280;
    }

    /* Add button styling */
    .ul-step-add{
        width:2.5rem;
        height:2.5rem;
        border-radius:50%;
        display:flex;
        align-items:center;
        justify-content:center;
        background:white;
        border:3px solid #d1d5db;
        box-shadow:0 2px 6px rgba(0,0,0,0.1);
        cursor:pointer;
        flex-shrink:0;
        z-index:4;
        position:relative;
    }
    
    /* Create inner circle effect for add button */
    .ul-step-add::before{
        content:"";
        position:absolute;
        left:50%;
        top:50%;
        transform:translate(-50%, -50%);
        width:4px;
        height:100%;
        background:#d1d5db;
        z-index:-1;
    }

    .ul-step-add svg{
        width:1.5rem;
        height:1.5rem;
        fill:#9ca3af;
    }

    .ul-step-add:hover{
        border-color:var(--visualization-blue-0);
    }

    .ul-step-add:hover svg{
        fill:var(--visualization-blue-0);
    }

    /* Menu dots container - prevents distortion from long text */
    .ul-menu-dots-container{
        flex-shrink:0;
        width:24px;
        height:100%;
        display:flex;
        align-items:center;
        justify-content:center;
        position:relative;
    }

    /* Six dots menu */
    .ul-menu-dots{
        cursor:pointer;
        padding:4px;
        color:var(--visualization-txt-5);
        font-size:0.6rem;
        line-height:1;
        display:flex;
        flex-direction:column;
        gap:0px;
        align-items:center;
        justify-content:center;
        position:relative;
        width:16px;
        height:16px;
    }
    
    .ul-menu-dots::before,
    .ul-menu-dots::after{
        content:"• •";
        letter-spacing:0.5px;
        display:block;
    }
    
    /* Add third row of dots using the actual content */
    .ul-menu-dots{
        font-size:0.6rem;
    }
    
    /* Dropdown menu */
    .ul-menu-dropdown{
        position:absolute;
        top:100%;
        right:0;
        background:white;
        border:1px solid var(--visualization-bg-2);
        border-radius:var(--size-radius-s);
        box-shadow:0 4px 12px rgba(0,0,0,0.15);
        min-width:150px;
        z-index:10;
        display:none;
    }
    
    .ul-menu-dots:hover .ul-menu-dropdown,
    .ul-menu-dropdown:hover{
        display:block;
    }
    
    .ul-menu-item{
        padding:var(--size-spacing-xs) var(--size-spacing-s);
        cursor:pointer;
        font-size:0.875rem;
        color:var(--visualization-txt-0);
        border-bottom:1px solid var(--visualization-bg-1);
    }
    
    .ul-menu-item:last-child{
        border-bottom:none;
    }
    
    .ul-menu-item:hover{
        background:var(--visualization-bg-1);
    }

    /* SVG timeline styling */
    .ul-timeline-svg{
        pointer-events:auto;
    }
    
    .insert-hover-zone{
        opacity:0;
        pointer-events:all;
    }
    
    .insert-hover-zone:hover{
        opacity:0.05;
        fill:rgba(37, 99, 235, 0.1);
    }
    
    .dynamic-insert-button{
        cursor:pointer;
        pointer-events:all;
        z-index:10;
    }
    
    .dynamic-insert-button:hover circle{
        stroke:#1d4ed8;
        fill:#1d4ed8;
    }

    .ul-menu-dots:hover{
        color:var(--visualization-txt-3);
    }

    .ul-card small{
        display:block;
        font-weight:400;
        color:var(--visualization-txt-5);
        margin-top:2px;
    }

    /* ─── "+ Add Action" button ───────────────────────────── */
    .ul-add-btn{
        margin-top:var(--size-spacing-s);
        background:none;
        border:0;
        cursor:pointer;
        color:var(--visualization-blue-1);
        font:inherit;
        padding:var(--size-spacing-xs);
        display:flex;
        align-items:center;
        gap:var(--size-spacing-xs);
    }

    .ul-add-btn{
        position:relative;
        padding-left:calc(3rem + var(--size-spacing-s));
        margin-top:var(--size-spacing-m);
    }

    .ul-add-btn:hover{
        text-decoration:underline;
    }

    /* ─── Test Condition links ────────────────────────────── */
    .ul-condition{
        color:#007acc;
        cursor:pointer;
        text-decoration:none;
        display:flex;
        align-items:center;
        justify-content:center;
        gap:var(--size-spacing-xs);
        font-weight:500;
        padding:var(--size-spacing-s);
        white-space:nowrap;
    }

    /* ─── Test Condition cards ────────────────────────────── */
    .ul-condition-card{
        background:var(--visualization-bg-0);
        border:3px solid var(--clr-teal-400);
        border-left:7px solid var(--clr-teal-500);
        border-radius: 5px;
        padding:var(--size-spacing-s) var(--size-spacing-m);
        font-weight:600;
        color:var(--visualization-txt-0);
        box-shadow:0 2px 4px rgb(0 0 0 / .1);
        position:relative;
        display:flex;
        justify-content:space-between;
        align-items:center;
        width:240px;
        height:52px;
        flex-shrink:0;
    }

    /* Failed test condition styling */
    .ul-condition-card.failed{
        border:3px solid #dc3545;
        border-left:7px solid #c82333;
        background:rgba(220, 53, 69, 0.05);
    }

    .ul-condition-card.failed .ul-card-content{
        color:#dc3545;
    }

    .ul-condition::before{
        content:"+";
        font-weight:700;
    }

    .ul-condition:hover{
        text-decoration:underline;
    }


</style>

<div class="userlane-step-visualization-page">
    <div class="visualization-content">
        <div class="userlane-chart-container" id="userlaneVisualizationChartContainer">
            <div id="loadGraphAndAssets"></div>
            
            <!-- Zoom controls -->
            <div class="zoom-controls" id="zoomControls">
                <button class="zoom-button" id="resetViewBtn" title="Reset View">⌂ Reset</button>
                <button class="zoom-button" id="zoomOutBtn">−</button>
                <div class="zoom-display" id="zoomDisplay">100%</div>
                <button class="zoom-button" id="zoomInBtn">+</button>
            </div>
            
            <!-- Zoomable and draggable content wrapper -->
            <div class="zoomable-content" id="zoomableContent">
                <!-- Action / Condition timeline -->
                <div class="ul-timeline" id="ulTimeline">
                    <div class="ul-actions" id="ulActions">
                        <div class="ul-column-header">ACTIONS</div>
                        <br>
                        <!-- SVG Timeline will be inserted here -->
                        <div class="ul-timeline-svg" id="timelineSvg"></div>
                        <!-- rows are injected here -->
                        <button type="button" class="ul-add-btn" onclick="openCreateStepPanel()">
                            Add Action
                        </button>
                    </div>

                    <div class="ul-conditions" id="ulConditions">
                        <div class="ul-column-header" id="conditionsHeader" style="visibility: hidden;">TEST CONDITIONS</div>
                        <br id="conditionsHeaderBr" style="visibility: hidden;">
                        <!-- rows are injected here -->
                    </div>
                </div>
            </div>

            <loading-component class="loading hide"></loading-component>
        </div>
    </div>
</div>

<!-- Create Panel for Steps -->
<vc:create-panel 
    entity="UserlaneStep" 
    route-name="UserlaneSteps" 
    parent-property-name="userlaneId" 
    parent-property-value="@Model.Userlane?.Id" 
    localizer="@localizer"></vc:create-panel>


<script>
// Wrap in namespace to prevent variable conflicts
(function() {
    'use strict';
    
    /* Zoom and Pan Handler */
    class ZoomHandler {
        constructor() {
            this.scale = 1;
            this.translateX = 0;
            this.translateY = 0;
            this.minScale = 0.25;
            this.maxScale = 2;
            this.isDragging = false;
            this.lastPointerX = 0;
            this.lastPointerY = 0;
            this.isTransitioning = false;
            this.pendingScale = null;
            this.debounceTimeout = null;
            
            this.container = document.getElementById('userlaneVisualizationChartContainer');
            this.content = document.getElementById('zoomableContent');
            this.zoomDisplay = document.getElementById('zoomDisplay');
            this.zoomInBtn = document.getElementById('zoomInBtn');
            this.zoomOutBtn = document.getElementById('zoomOutBtn');
            this.resetViewBtn = document.getElementById('resetViewBtn');
            
            this.init();
        }
        
        init() {
            if (!this.container || !this.content) return;
            
            // Prevent default drag behavior on images and other elements
            this.container.addEventListener('dragstart', (e) => e.preventDefault());
            
            // Mouse events
            this.container.addEventListener('mousedown', this.handlePointerDown.bind(this));
            this.container.addEventListener('mousemove', this.handlePointerMove.bind(this));
            this.container.addEventListener('mouseup', this.handlePointerUp.bind(this));
            this.container.addEventListener('mouseleave', this.handlePointerUp.bind(this));
            
            // Touch events
            this.container.addEventListener('touchstart', this.handlePointerDown.bind(this), { passive: false });
            this.container.addEventListener('touchmove', this.handlePointerMove.bind(this), { passive: false });
            this.container.addEventListener('touchend', this.handlePointerUp.bind(this));
            
            // Wheel event for zooming
            this.container.addEventListener('wheel', this.handleWheel.bind(this), { passive: false });
            
            // Zoom button events
            if (this.zoomInBtn) this.zoomInBtn.addEventListener('click', this.handleZoomIn.bind(this));
            if (this.zoomOutBtn) this.zoomOutBtn.addEventListener('click', this.handleZoomOut.bind(this));
            if (this.resetViewBtn) this.resetViewBtn.addEventListener('click', this.resetZoom.bind(this));
            
            this.updateTransform();
        }
        
        getPointerPos(event) {
            if (event.touches && event.touches.length > 0) {
                return { x: event.touches[0].clientX, y: event.touches[0].clientY };
            }
            return { x: event.clientX, y: event.clientY };
        }
        
        handlePointerDown(event) {
            // Don't drag if clicking on interactive elements
            if (event.target.closest('.zoom-controls, .ul-menu-dots, .ul-add-btn, .ul-card, .ul-condition-card, .ul-condition')) {
                return;
            }
            
            event.preventDefault();
            this.isDragging = true;
            this.container.classList.add('dragging');
            
            const pos = this.getPointerPos(event);
            this.lastPointerX = pos.x;
            this.lastPointerY = pos.y;
        }
        
        handlePointerMove(event) {
            if (!this.isDragging) return;
            
            event.preventDefault();
            const pos = this.getPointerPos(event);
            const deltaX = pos.x - this.lastPointerX;
            const deltaY = pos.y - this.lastPointerY;
            
            this.translateX += deltaX;
            this.translateY += deltaY;
            
            this.lastPointerX = pos.x;
            this.lastPointerY = pos.y;
            
            this.updateTransform();
        }
        
        handlePointerUp(event) {
            this.isDragging = false;
            this.container.classList.remove('dragging');
        }
        
        handleWheel(event) {
            event.preventDefault();
            
            const rect = this.container.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;
            
            const delta = event.deltaY > 0 ? -0.1 : 0.1;
            const newScale = Math.max(this.minScale, Math.min(this.maxScale, this.scale + delta));
            
            if (newScale !== this.scale) {
                this.zoomToPoint(newScale, x, y);
            }
        }
        
        zoomToPoint(newScale, x, y) {
            const scaleDiff = newScale / this.scale;
            
            this.translateX = x - scaleDiff * (x - this.translateX);
            this.translateY = y - scaleDiff * (y - this.translateY);
            this.scale = newScale;
            
            this.updateTransform();
            this.updateZoomDisplay();
        }
        
        handleZoomIn() {
            const newScale = Math.min(this.maxScale, this.scale + 0.1);
            if (newScale !== this.scale) {
                this.debouncedZoom(newScale);
            }
        }
        
        handleZoomOut() {
            const newScale = Math.max(this.minScale, this.scale - 0.1);
            if (newScale !== this.scale) {
                this.debouncedZoom(newScale);
            }
        }
        
        debouncedZoom(targetScale, delay = 50) {
            if (this.debounceTimeout !== null) {
                clearTimeout(this.debounceTimeout);
            }
            
            if (this.isTransitioning) {
                this.pendingScale = targetScale;
                return;
            }
            
            this.debounceTimeout = setTimeout(() => {
                this.applyZoom(targetScale);
                this.debounceTimeout = null;
            }, delay);
        }
        
        applyZoom(scale) {
            const boundedScale = Math.max(this.minScale, Math.min(this.maxScale, scale));
            const rect = this.container.getBoundingClientRect();
            const centerX = rect.width / 2;
            const centerY = rect.height / 2;
            
            this.zoomToPoint(boundedScale, centerX, centerY);
        }
        
        resetZoom() {
            if (this.debounceTimeout !== null) {
                clearTimeout(this.debounceTimeout);
                this.debounceTimeout = null;
            }
            this.pendingScale = null;
            
            this.isTransitioning = true;
            this.content.style.transition = 'transform 0.3s ease-out';
            
            this.scale = 1;
            this.translateX = 0;
            this.translateY = 0;
            
            this.updateTransform();
            this.updateZoomDisplay();
            
            setTimeout(() => {
                this.content.style.transition = 'transform 0.1s ease-out';
                this.isTransitioning = false;
            }, 300);
        }
        
        updateTransform() {
            if (!this.content) return;
            this.content.style.transform = `translate(${this.translateX}px, ${this.translateY}px) scale(${this.scale})`;
        }
        
        updateZoomDisplay() {
            if (this.zoomDisplay) {
                this.zoomDisplay.textContent = `${Math.round(this.scale * 100)}%`;
            }
            window.zoomScale = this.scale;
        }
        
        destroy() {
            if (this.debounceTimeout !== null) {
                clearTimeout(this.debounceTimeout);
                this.debounceTimeout = null;
            }
            this.pendingScale = null;
            this.isTransitioning = false;
        }
    }
    
    /* Initialize zoom handler */
    let zoomHandler = null;
    
    /* Current UserlaneId and logging */
    let currentUserlaneId = '@Model.Userlane?.Id';
    console.log('UserlaneId from model:', currentUserlaneId);
    
    // If model ID is empty, extract from URL
    if (!currentUserlaneId || currentUserlaneId === '' || currentUserlaneId === 'null' || currentUserlaneId === 'undefined') {
        console.log('Model ID not available, extracting from URL...');
        const urlPath = window.location.pathname;
        const urlMatch = urlPath.match(/\/Admin\/Userlane\/([a-f0-9-]{36})/i);
        if (urlMatch && urlMatch[1]) {
            currentUserlaneId = urlMatch[1];
            console.log('Extracted Userlane ID from URL:', currentUserlaneId);
        }
    }
    
    console.log('Final UserlaneId:', currentUserlaneId);
    console.log('Route Path:', '@Context.Request.Path');
    console.log('Route Values:', JSON.stringify(@Html.Raw(Json.Serialize(Context.Request.RouteValues))));
    
    // Auto-load steps when script executes if we have a valid Userlane ID
    if (currentUserlaneId && currentUserlaneId !== '' && currentUserlaneId !== 'null' && currentUserlaneId !== 'undefined') {
        console.log('🚀 Auto-loading steps with Userlane ID:', currentUserlaneId);
        setTimeout(() => {
            loadSteps();
        }, 100);
    } else {
        console.warn('❌ No valid Userlane ID available for auto-loading steps');
    }
    
    /* Test Conditions Store - stores test conditions by step ID */
    const testConditionsStore = new Map();
    
    /* Test Condition Results Store - stores the latest test results by condition ID */
    const testConditionResultsStore = new Map();
    
    /* Order Management Functions */
    function calculateNextOrder() {
        const actionCards = document.querySelectorAll('.ul-card-wrapper[data-order]');
        if (actionCards.length === 0) {
            console.log('📊 No existing actions, starting with order 10');
            return 10;
        }
        
        // Find the highest order
        let maxOrder = 0;
        actionCards.forEach(card => {
            const order = parseInt(card.getAttribute('data-order')) || 0;
            if (order > maxOrder) {
                maxOrder = order;
            }
        });
        
        const nextOrder = maxOrder + 10;
        console.log(`📊 Calculated next order: ${nextOrder} (last was ${maxOrder})`);
        return nextOrder;
    }
    
    function calculateInsertOrder(afterIndex) {
        const actionCards = document.querySelectorAll('.ul-card-wrapper[data-order]');
        
        if (afterIndex >= actionCards.length - 1) {
            // Inserting at the end
            return calculateNextOrder();
        }
        
        const currentOrder = parseInt(actionCards[afterIndex].getAttribute('data-order')) || 0;
        const nextOrder = currentOrder + 1;
        
        console.log(`📊 Calculated insert order: ${nextOrder} (after ${currentOrder})`);
        return nextOrder;
    }

    /* helpers ­––––––––––––––––––––––––––––––––––––––––– */
    let actionCounter = 0;
    
    function getCursorIcon() {
        return `<svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M13,9H18.5L13,3.5V9M6,2H14L20,8V20A2,2 0 0,1 18,22H6C4.89,22 4,21.1 4,20V4C4,2.89 4.89,2 6,2M15,18V16H6V18H15M18,14V12H6V14H18Z"/>
        </svg>`;
    }
    
    function getPlusIcon() {
        return `<svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
        </svg>`;
    }
    
    function updateConditionsHeaderVisibility() {
        const conditionsContainer = document.getElementById('ulConditions');
        const conditionsHeader = document.getElementById('conditionsHeader');
        const conditionsHeaderBr = document.getElementById('conditionsHeaderBr');
        const conditionCards = conditionsContainer.querySelectorAll('.ul-condition-card');
        
        if (conditionCards.length > 0) {
            conditionsHeader.style.visibility = 'visible';
            conditionsHeaderBr.style.visibility = 'visible';
        } else {
            conditionsHeader.style.visibility = 'hidden';
            conditionsHeaderBr.style.visibility = 'hidden';
        }
    }
    
    function updateTimeline() {
        const timelineSvg = document.getElementById('timelineSvg');
        const actions = document.querySelectorAll('.ul-card-wrapper');
        const addBtn = document.querySelector('.ul-add-btn');
        
        if (!timelineSvg) return;
        
        // Debug: Log the number of actions found
        console.log(`Updating timeline with ${actions.length} actions`);
        
        // Calculate positions
        const positions = [];
        const timelineRect = timelineSvg.getBoundingClientRect();
        
        actions.forEach((action, index) => {
            const rect = action.getBoundingClientRect();
            const position = rect.top - timelineRect.top + rect.height / 2;
            positions.push(position);
            console.log(`Action ${index}: position ${position}`);
        });
        
        // Add position for the add button
        if (addBtn) {
            const rect = addBtn.getBoundingClientRect();
            const position = rect.top - timelineRect.top + rect.height / 2;
            positions.push(position);
            console.log(`Add button: position ${position}`);
        }
        
        // Create SVG with line and circles
        const height = Math.max(timelineSvg.offsetHeight, 400);
        const width = 48; // 3rem = 48px
        const centerX = width / 2;
        
        let svgContent = `<svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" style="overflow: visible;">`;
        
        // Main timeline line
        if (positions.length > 0) {
            const startY = Math.max(0, positions[0] - 20);
            const endY = Math.min(height, positions[positions.length - 1] + 20);
            svgContent += `<line id="timeline-main-line" x1="${centerX}" y1="${startY}" x2="${centerX}" y2="${endY}" stroke="#d1d5db" stroke-width="2" style="transition: all 0.3s ease;"/>`;
        }
        
        // Add circles at each position - no pre-calculated expansion
        console.log(`Creating ${positions.length} circles and icons`);
        positions.forEach((y, index) => {
            svgContent += `<circle cx="${centerX}" cy="${y}" r="20" fill="white" stroke="#d1d5db" stroke-width="3" style="filter: drop-shadow(0 2px 6px rgba(0,0,0,0.1));" data-row-index="${index}"/>`;
            
            // Check if this is the last position (Add Action button)
            const isAddButton = index === positions.length - 1;
            
            if (isAddButton) {
                // Add plus icon for Add Action button
                svgContent += `<g transform="translate(${centerX-6}, ${y-6})" data-row-index="${index}">
                    <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" fill="#007acc" transform="scale(0.5)"/>
                </g>`;
            } else {
                // Add lightbulb-gear icon for existing steps
                svgContent += `<g transform="translate(${centerX-8}, ${y-8})" data-row-index="${index}">
                    <g transform="scale(0.67)">
                      <path d="M12,2A7,7 0 0,0 5,9C5,11.38 6.19,13.47 8,14.74V17A1,1 0 0,0 9,18H15A1,1 0 0,0 16,17V14.74C17.81,13.47 19,11.38 19,9A7,7 0 0,0 12,2M9,21A1,1 0 0,0 10,22H14A1,1 0 0,0 15,21V20H9V21Z" 
                            fill="none" stroke="#007acc" stroke-width="1.5"/>
                      <g transform="translate(5, 6)">
                        <path d="M15.5,12L14,10.5L15.5,9L17,10.5L15.5,12M19.43,12.57C19.79,12.21 19.79,11.63 19.43,11.27L18.66,10.5L19.43,9.73C19.79,9.37 19.79,8.79 19.43,8.43L17.57,6.57C17.21,6.21 16.63,6.21 16.27,6.57L15.5,7.34L14.73,6.57C14.37,6.21 13.79,6.21 13.43,6.57L11.57,8.43C11.21,8.79 11.21,9.37 11.57,9.73L12.34,10.5L11.57,11.27C11.21,11.63 11.21,12.21 11.57,12.57L13.43,14.43C13.79,14.79 14.37,14.79 14.73,14.43L15.5,13.66L16.27,14.43C16.63,14.79 17.21,14.79 17.57,14.43L19.43,12.57Z" 
                              fill="none" stroke="#007acc" stroke-width="1"/>
                      </g>
                    </g>
                </g>`;
            }
            console.log(`Created circle and ${isAddButton ? 'plus' : 'lightbulb-gear'} icon ${index} at position ${y}`);
            
            // Add individual hover zones between circles (except after last)
            if (index < positions.length - 1) {
                const nextY = positions[index + 1];
                const midpointY = y + ((nextY - y) * 0.5);
                const hoverZoneHeight = Math.abs(nextY - y);
                
                svgContent += `<rect class="insert-hover-zone" 
                    x="${centerX - 24}" 
                    y="${y + 25}" 
                    width="48" 
                    height="${hoverZoneHeight - 50}" 
                    fill="transparent" 
                    data-insert-after="${index}"
                    data-hover-y="${midpointY}"
                    style="cursor: pointer;"/>`;
            }
        });
        
        svgContent += '</svg>';
        
        timelineSvg.innerHTML = svgContent;
        
        // Add individual hover zone handlers
        addIndividualHoverHandlers();
    }
    
    function addIndividualHoverHandlers() {
        const hoverZones = document.querySelectorAll('.insert-hover-zone');
        let currentInsertButton = null;
        let hoverTimeout = null;
        
        hoverZones.forEach(zone => {
            zone.addEventListener('mouseenter', function() {
                clearTimeout(hoverTimeout);
                
                const insertAfterIndex = parseInt(this.getAttribute('data-insert-after'));
                const hoverY = parseFloat(this.getAttribute('data-hover-y'));
                
                // Remove any existing insert button
                removeInsertButton();
                
                // Create insert button at the exact hover position (no expansion needed)
                currentInsertButton = createInsertButton(hoverY, insertAfterIndex);
            });
            
            zone.addEventListener('mouseleave', function(e) {
                // Check if mouse is moving to the insert button
                const insertButton = document.querySelector('.dynamic-insert-button');
                if (insertButton && insertButton.contains(e.relatedTarget)) {
                    return; // Don't remove if moving to button
                }
                
                hoverTimeout = setTimeout(() => {
                    // Just remove insert button, no row contraction needed
                    removeInsertButton();
                }, 100);
            });
        });
    }
    
    function createInsertButton(y, insertAfterIndex) {
        const timelineSvg = document.getElementById('timelineSvg');
        const svg = timelineSvg.querySelector('svg');
        const centerX = 24;
        
        const insertGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
        insertGroup.setAttribute('class', 'dynamic-insert-button');
        insertGroup.setAttribute('data-insert-after', insertAfterIndex);
        insertGroup.style.cursor = 'pointer';
        
        // Create horizontal blue line extending to action card area
        const horizontalLine = document.createElementNS('http://www.w3.org/2000/svg', 'line');
        horizontalLine.setAttribute('x1', centerX + 20); // Start from right edge of button
        horizontalLine.setAttribute('y1', y);
        horizontalLine.setAttribute('x2', centerX + 350); // Extended by 3px (320 + 3)
        horizontalLine.setAttribute('y2', y);
        horizontalLine.setAttribute('stroke', '#2563eb');
        horizontalLine.setAttribute('stroke-width', '4'); // Made thicker (2px -> 4px)
        horizontalLine.style.opacity = '0.7';
        
        // Create larger clickable area
        const clickArea = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        clickArea.setAttribute('cx', centerX);
        clickArea.setAttribute('cy', y);
        clickArea.setAttribute('r', '20');
        clickArea.setAttribute('fill', 'transparent');
        clickArea.setAttribute('stroke', 'none');
        
        // Create visible button
        const visibleButton = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        visibleButton.setAttribute('cx', centerX);
        visibleButton.setAttribute('cy', y);
        visibleButton.setAttribute('r', '14');
        visibleButton.setAttribute('fill', '#2563eb'); // Changed to blue fill
        visibleButton.setAttribute('stroke', '#2563eb');
        visibleButton.setAttribute('stroke-width', '2');
        visibleButton.style.filter = 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))';
        
        // Create plus text
        const plusText = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        plusText.setAttribute('x', centerX);
        plusText.setAttribute('y', y + 4);
        plusText.setAttribute('text-anchor', 'middle');
        plusText.setAttribute('fill', 'white'); // Changed to white text
        plusText.setAttribute('font-size', '14');
        plusText.setAttribute('font-weight', 'bold');
        plusText.setAttribute('pointer-events', 'none');
        plusText.textContent = '+';
        
        insertGroup.appendChild(horizontalLine);
        insertGroup.appendChild(clickArea);
        insertGroup.appendChild(visibleButton);
        insertGroup.appendChild(plusText);
        
        // Add event handlers
        insertGroup.addEventListener('click', function(e) {
            e.stopPropagation();
            const afterIndex = parseInt(this.getAttribute('data-insert-after'));
            console.log(`🔗 Insert button clicked, after index: ${afterIndex}`);
            openCreateStepPanel(afterIndex);
        });
        
        insertGroup.addEventListener('mouseleave', function() {
            setTimeout(() => {
                removeInsertButton();
            }, 200);
        });
        
        svg.appendChild(insertGroup);
        return insertGroup;
    }
    
    function removeInsertButton() {
        const existingButton = document.querySelector('.dynamic-insert-button');
        if (existingButton) {
            existingButton.remove();
        }
    }
    
    
    function updateSVGHeight() {
        const timelineSvg = document.getElementById('timelineSvg');
        const svg = timelineSvg.querySelector('svg');
        if (svg) {
            const actions = document.querySelectorAll('.ul-card-wrapper');
            if (actions.length > 0) {
                const lastAction = actions[actions.length - 1];
                const rect = lastAction.getBoundingClientRect();
                const containerRect = timelineSvg.getBoundingClientRect();
                const newHeight = Math.max(400, rect.bottom - containerRect.top + 100);
                svg.setAttribute('height', newHeight);
                svg.setAttribute('viewBox', `0 0 48 ${newHeight}`);
            }
        }
    }
    
    function createAction(){
        actionCounter++;
        const wrapper = document.createElement('div');
        wrapper.className = 'ul-card-wrapper';
        wrapper.setAttribute('data-action-id', actionCounter);
        
        const div = document.createElement('div');
        div.className = 'ul-card';
        div.innerHTML = `
            <div class="ul-card-content">
                Click<br>
                <small>Press Button</small>
            </div>
            <div class="ul-menu-dots-container">
                <div class="ul-menu-dots">• •
                    <div class="ul-menu-dropdown">
                        <div class="ul-menu-item">Edit</div>
                        <div class="ul-menu-item">Duplicate</div>
                        <div class="ul-menu-item">Delete</div>
                    </div>
                </div>
            </div>
        `;
        
        wrapper.appendChild(div);
        
        // Update timeline after adding - use longer timeout to ensure DOM is updated
        setTimeout(() => {
            // Force a reflow to ensure new elements are properly positioned
            wrapper.offsetHeight;
            updateTimeline();
        }, 200);
        
        return wrapper;
    }
    
    function insertActionAtIndex(afterIndex) {
        const actions = document.getElementById('ulActions');
        const actionWrappers = actions.querySelectorAll('.ul-card-wrapper');
        
        // Create new action wrapper directly (without calling createAction to avoid recursion)
        actionCounter++;
        const wrapper = document.createElement('div');
        wrapper.className = 'ul-card-wrapper';
        wrapper.setAttribute('data-action-id', actionCounter);
        
        const div = document.createElement('div');
        div.className = 'ul-card';
        div.innerHTML = `
            <div class="ul-card-content">
                Click<br>
                <small>Press Button</small>
            </div>
            <div class="ul-menu-dots-container">
                <div class="ul-menu-dots">• •
                    <div class="ul-menu-dropdown">
                        <div class="ul-menu-item">Edit</div>
                        <div class="ul-menu-item">Duplicate</div>
                        <div class="ul-menu-item">Delete</div>
                    </div>
                </div>
            </div>
        `;
        
        wrapper.appendChild(div);
        
        if (afterIndex < actionWrappers.length - 1) {
            // Insert after the specified index
            actions.insertBefore(wrapper, actionWrappers[afterIndex + 1]);
        } else {
            // Insert before the add button
            const addBtn = actions.querySelector('.ul-add-btn');
            actions.insertBefore(wrapper, addBtn);
        }
        
        // Add corresponding test condition row
        const conditions = document.getElementById('ulConditions');
        const conditionRows = conditions.querySelectorAll('.ul-condition-row');
        if (afterIndex < conditionRows.length - 1) {
            conditions.insertBefore(createConditionRow(null), conditionRows[afterIndex + 1]);
        } else {
            conditions.appendChild(createConditionRow(null));
        }
        
        // Update timeline after adding - use longer timeout to ensure DOM is updated
        setTimeout(() => {
            // Force a reflow to ensure new elements are properly positioned
            wrapper.offsetHeight;
            updateTimeline();
        }, 200);
    }

    function createCondition(stepId){
        const link = document.createElement('div');
        link.className = 'ul-condition';
        link.textContent = 'Test Condition';
        link.setAttribute('data-step-id', stepId);
        link.addEventListener('click', function() {
            const stepId = this.getAttribute('data-step-id');
            if (stepId) {
                openCreateTestConditionPanel(stepId);
            } else {
                console.error('No step ID found for test condition');
            }
        });
        return link;
    }

    function createConditionRow(stepId, testConditions = []){
        const row = document.createElement('div');
        row.className = 'ul-condition-row';
        row.setAttribute('data-step-id', stepId);
        
        console.log(`🏗️ Creating condition row for step ${stepId} with ${testConditions ? testConditions.length : 0} test conditions`);
        
        // Add existing test conditions as cards
        if (testConditions && testConditions.length > 0) {
            testConditions.forEach((condition, index) => {
                console.log(`  📋 Adding test condition ${index + 1} to row for step ${stepId}:`);
                console.log(`     - Condition ID: ${condition.id}`);
                console.log(`     - Condition belongs to step: ${condition.userlaneStepId}`);
                console.log(`     - Current row is for step: ${stepId}`);
                console.log(`     - Match: ${condition.userlaneStepId === stepId ? '✅' : '❌'}`);
                
                const conditionCard = createTestConditionCard(condition);
                row.appendChild(conditionCard);
            });
        } else {
            console.log(`  📭 No test conditions to add for step ${stepId}`);
        }
        
        // Always add the "Add Test Condition" button at the end
        const addConditionBtn = createCondition(stepId);
        row.appendChild(addConditionBtn);
        
        console.log(`✅ Completed condition row for step ${stepId}`);
        return row;
    }

    function createTestConditionCard(testCondition){
        const card = document.createElement('div');
        card.className = 'ul-condition-card';
        card.setAttribute('data-condition-id', testCondition.id);
        
        // Check if this test condition failed and apply failed styling
        if (isTestConditionFailed(testCondition.id)) {
            card.classList.add('failed');
            console.log(`❌ Test condition ${testCondition.id} marked as failed`);
        } else {
            console.log(`✅ Test condition ${testCondition.id} passed or no result available`);
        }
        
        // Format the condition display based on condition type
        let conditionDisplay = '';
        let conditionDetails = '';
        
        // Helper function to get field name (use fieldName if available, otherwise field)
        const getFieldDisplayName = (testCondition) => {
            return testCondition.fieldName || testCondition.field || 'Field';
        };
        
        switch(testCondition.conditionType) {
            case 'FieldComparison':
                conditionDisplay = 'Field Comparison';
                conditionDetails = `${getFieldDisplayName(testCondition)} ${testCondition.operator || '='} ${testCondition.value || 'Value'}`;
                break;
            case 'ElementExists':
                conditionDisplay = 'Element Exists';
                conditionDetails = getFieldDisplayName(testCondition);
                break;
            case 'ElementVisible':
                conditionDisplay = 'Element Visible';
                conditionDetails = getFieldDisplayName(testCondition);
                break;
            case 'TextContains':
                conditionDisplay = 'Text Contains';
                conditionDetails = `${getFieldDisplayName(testCondition)} contains "${testCondition.value || 'text'}"`;
                break;
            case 'ValueEquals':
                conditionDisplay = 'Value Equals';
                conditionDetails = `${getFieldDisplayName(testCondition)} = "${testCondition.value || 'value'}"`;
                break;
            default:
                conditionDisplay = testCondition.conditionType || 'Test Condition';
                conditionDetails = 'Click to edit';
        }
        
        card.innerHTML = `
            <div class="ul-card-content" onclick="editTestCondition('${testCondition.id}')">
                ${conditionDisplay}<br>
                <small>${conditionDetails}</small>
            </div>
            <div class="ul-menu-dots-container">
                <div class="ul-menu-dots">• •
                    <div class="ul-menu-dropdown">
                        <div class="ul-menu-item" onclick="editTestCondition('${testCondition.id}')">Edit</div>
                        <div class="ul-menu-item" onclick="deleteTestCondition('${testCondition.id}')">Delete</div>
                    </div>
                </div>
            </div>
        `;
        
        // Update header visibility when a condition card is created
        setTimeout(() => {
            updateConditionsHeaderVisibility();
        }, 10);
        
        return card;
    }

    function createConditionCard(){
        const card = document.createElement('div');
        card.className = 'ul-condition-card';
        card.innerHTML = `
            <div>
                Field Comparison<br>
                <small>Field • Value</small>
            </div>
            <div class="ul-menu-dots">• •
                <div class="ul-menu-dropdown">
                    <div class="ul-menu-item">Edit</div>
                    <div class="ul-menu-item">Duplicate</div>
                    <div class="ul-menu-item" onclick="deleteConditionCard(this)">Delete</div>
                </div>
            </div>
        `;
        
        // Update header visibility when a condition card is created
        setTimeout(() => {
            updateConditionsHeaderVisibility();
        }, 10);
        
        return card;
    }
    
    function editTestCondition(conditionId) {
        // Navigate to the test condition edit page
        Page.setMainPage(`/Admin/Userlane/Step/TestCondition/${conditionId}`);
        Page.setInfo(`/Admin/Userlane/Step/TestCondition/${conditionId}`);
        Page.load(`/Admin/Userlane/Step/TestCondition/${conditionId}`, {}, {getRootNode: document.querySelector('main')});
    }
    
    async function deleteTestCondition(conditionId) {
        if (confirm('Are you sure you want to delete this test condition?')) {
            try {
                const response = await fetch(`/Api/UserlaneStepTestCondition/${conditionId}`, {
                    method: 'DELETE'
                });
                
                if (response.ok) {
                    // Reload steps to refresh the test conditions
                    loadSteps();
                } else {
                    alert('Error deleting test condition');
                }
            } catch (error) {
                console.error('Error deleting test condition:', error);
                alert('Error deleting test condition');
            }
        }
    }

    function deleteConditionCard(deleteBtn) {
        const card = deleteBtn.closest('.ul-condition-card');
        if (card) {
            card.remove();
            // Update header visibility when a condition card is deleted
            setTimeout(() => {
                updateConditionsHeaderVisibility();
            }, 10);
        }
    }

    function openCreateStepPanel(insertAfterIndex = null) {
        console.log('🎯 Opening step creation panel...');
        
        // Calculate the order for the new step
        let newOrder;
        if (insertAfterIndex !== null) {
            newOrder = calculateInsertOrder(insertAfterIndex);
            console.log(`📊 Creating step to insert after index ${insertAfterIndex} with order ${newOrder}`);
        } else {
            newOrder = calculateNextOrder();
            console.log(`📊 Creating step at end with order ${newOrder}`);
        }
        
        // Store the calculated order for use when the panel opens
        window.pendingStepOrder = newOrder;
        
        // Try multiple approaches to open the step creation panel
        
        // 1. Try the FAB button first
        const fabButton = document.querySelector('fab-component[data-action=add]');
        if (fabButton) {
            console.log('✅ Found FAB button, clicking it');
            fabButton.click();
            return;
        }
        
        // 2. Try lvl-fab as fallback
        const lvlFabButton = document.querySelector('lvl-fab[data-action=add]');
        if (lvlFabButton) {
            console.log('✅ Found lvl-fab button, clicking it');
            lvlFabButton.click();
            return;
        }
        
        // 3. Direct panel opening as last resort
        console.log('❌ No FAB button found, trying direct panel opening');
        const createPanel = document.getElementById('create-panel');
        if (createPanel) {
            console.log('✅ Found create panel, opening directly');
            createPanel.setAttribute('open', 'true');
            // Trigger the create panel's internal logic
            const event = new CustomEvent('open');
            createPanel.dispatchEvent(event);
        } else {
            console.error('❌ Create panel not found');
            alert('Unable to open step creation panel');
        }
    }

    /* public API – call addRow() to insert a new pair (kept for compatibility) */
    function addRow(){
        // Now this just opens the create panel instead of adding directly
        openCreateStepPanel();
    }

    /* Load existing steps from API and adapt them to this editor */
    async function loadSteps() {
        console.log('loadSteps called');
        console.log('currentUserlaneId:', currentUserlaneId);
        
        if (!currentUserlaneId) {
            console.warn('No UserlaneId found, aborting loadSteps');
            return;
        }
        
        try {
            // First, load all test conditions for this userlane into the store
            await loadAllTestConditions();
            
            // Load the latest test condition results
            await loadLatestTestConditionResults();
            
            // Then load the steps
            const apiUrl = `/Api/UserlaneSteps?filters[0][filterColumn]=UserlaneId&filters[0][operator]=Equals&filters[0][compareValue]=${currentUserlaneId}`;
            console.log('Fetching steps from URL:', apiUrl);
            
            const response = await fetch(apiUrl);
            console.log('Response status:', response.status);
            console.log('Response ok:', response.ok);
            
            const data = await response.json();
            console.log('Response data:', data);
            console.log('Data success:', data.success);
            console.log('Data.data:', data.data);
            console.log('Data.data length:', data.data?.length);
            
            if (data.data && data.data.rows) {
                console.log('Calling renderSteps with', data.data.rows.length, 'steps');
                await renderSteps(data.data.rows);
            } else {
                console.warn('No valid data received or data.data.rows not found');
            }
        } catch (error) {
            console.error('Error loading steps:', error);
        }
    }
    
    /* Load ALL test conditions for the current userlane and store them */
    async function loadAllTestConditions() {
        try {
            // Load all test conditions for this userlane
            const apiUrl = `/Api/UserlaneStepTestCondition`;
            console.log(`🔍 Loading ALL test conditions for userlane: ${currentUserlaneId}`);
            console.log('API URL:', apiUrl);
            
            const response = await fetch(apiUrl);
            const data = await response.json();
            
            if (data.data && data.data.rows) {
                console.log(`✅ Found ${data.data.rows.length} total test conditions`);
                
                // Clear the store
                testConditionsStore.clear();
                
                // Group test conditions by step ID
                data.data.rows.forEach((tc, i) => {
                    console.log(`  ${i+1}. Condition ID: ${tc.id}, UserlaneStepId: ${tc.userlaneStepId}, Type: ${tc.conditionType}`);
                    
                    if (!testConditionsStore.has(tc.userlaneStepId)) {
                        testConditionsStore.set(tc.userlaneStepId, []);
                    }
                    testConditionsStore.get(tc.userlaneStepId).push(tc);
                });
                
                console.log(`📦 Stored test conditions for ${testConditionsStore.size} steps`);
                testConditionsStore.forEach((conditions, stepId) => {
                    console.log(`  Step ${stepId}: ${conditions.length} conditions`);
                });
                
            } else {
                console.log(`❌ No test conditions found`);
                testConditionsStore.clear();
            }
        } catch (error) {
            console.error('Error loading test conditions:', error);
            testConditionsStore.clear();
        }
    }
    
    /* Get test conditions for a specific step from the store */
    function getTestConditionsForStep(stepId) {
        const conditions = testConditionsStore.get(stepId) || [];
        console.log(`📋 Retrieved ${conditions.length} test conditions for step ${stepId} from store`);
        return conditions;
    }
    
    /* Load the latest test condition results for this userlane */
    async function loadLatestTestConditionResults() {
        try {
            // Get the most recent batch for this userlane
            const batchUrl = `/Api/UserlaneResultBatch?filters[0].FilterColumn=UserlaneId&filters[0].CompareValue=${currentUserlaneId}&pageSize=1&sortColumn=CreatedDateTime&sortDirection=desc`;
            console.log(`🔍 Loading latest test results batch for userlane: ${currentUserlaneId}`);
            console.log('Batch API URL:', batchUrl);
            
            const batchResponse = await fetch(batchUrl);
            const batchData = await batchResponse.json();
            
            if (batchData.data && batchData.data.rows && batchData.data.rows.length > 0) {
                const latestBatch = batchData.data.rows[0];
                console.log(`✅ Found latest batch: ${latestBatch.id}`);
                
                // Load test condition results for this batch
                const resultsUrl = `/Api/UserlaneStepTestConditionResult?filters[0].FilterColumn=BatchId&filters[0].CompareValue=${latestBatch.id}&pageSize=1000`;
                console.log('Results API URL:', resultsUrl);
                
                const resultsResponse = await fetch(resultsUrl);
                const resultsData = await resultsResponse.json();
                
                if (resultsData.data && resultsData.data.rows) {
                    console.log(`✅ Found ${resultsData.data.rows.length} test condition results`);
                    
                    // Clear the results store
                    testConditionResultsStore.clear();
                    
                    // Store results by condition ID
                    resultsData.data.rows.forEach((result, i) => {
                        console.log(`  ${i+1}. Condition ID: ${result.userlaneStepTestConditionId}, Passed: ${result.passed}`);
                        testConditionResultsStore.set(result.userlaneStepTestConditionId, result);
                    });
                    
                    console.log(`📦 Stored test results for ${testConditionResultsStore.size} conditions`);
                } else {
                    console.log(`❌ No test condition results found for batch ${latestBatch.id}`);
                    testConditionResultsStore.clear();
                }
            } else {
                console.log(`❌ No result batches found for userlane ${currentUserlaneId}`);
                testConditionResultsStore.clear();
            }
        } catch (error) {
            console.error('Error loading test condition results:', error);
            testConditionResultsStore.clear();
        }
    }
    
    /* Check if a test condition failed based on latest results */
    function isTestConditionFailed(conditionId) {
        const result = testConditionResultsStore.get(conditionId);
        return result && !result.passed;
    }
    
    async function renderSteps(steps) {
        const actions = document.getElementById('ulActions');
        const addBtn = actions.querySelector('.ul-add-btn');
        const conditions = document.getElementById('ulConditions');
        
        // Clear existing cards
        const existingCards = actions.querySelectorAll('.ul-card-wrapper');
        existingCards.forEach(card => card.remove());
        
        const existingRows = conditions.querySelectorAll('.ul-condition-row');
        existingRows.forEach(row => row.remove());
        
        // Filter steps to only include those belonging to the current userlane
        const filteredSteps = steps.filter(step => {
            const belongsToUserlane = step.userlaneId === currentUserlaneId;
            if (!belongsToUserlane) {
                console.log(`🚫 Filtering out step ${step.id} - belongs to userlane ${step.userlaneId}, current is ${currentUserlaneId}`);
            }
            return belongsToUserlane;
        });
        
        console.log(`🔍 Filtered ${steps.length} steps to ${filteredSteps.length} steps for current userlane ${currentUserlaneId}`);
        
        // Sort filtered steps by order field
        const sortedSteps = [...filteredSteps].sort((a, b) => (a.order || 0) - (b.order || 0));
        console.log(`📊 Ordered ${sortedSteps.length} steps by order field:`);
        sortedSteps.forEach((step, i) => {
            console.log(`  ${i + 1}. Step ${step.id} - Order: ${step.order}, UserlaneId: ${step.userlaneId}, Title: ${step.title}`);
        });
        
        // Add each step as an action card and get its test conditions from store
        for (let i = 0; i < sortedSteps.length; i++) {
            const step = sortedSteps[i];
            console.log(`\n--- Processing Step ${i + 1}: ${step.id} (Order: ${step.order}) ---`);
            console.log('Step data:', step);
            
            const stepElement = createStepCard(step, i);
            stepElement.setAttribute('data-order', step.order); // Store order for later use
            actions.insertBefore(stepElement, addBtn);
            
            // Get test conditions for this specific step from the store
            const testConditions = getTestConditionsForStep(step.id);
            console.log(`Retrieved ${testConditions.length} test conditions for step ${step.id} from store:`);
            if (testConditions.length > 0) {
                testConditions.forEach((tc, idx) => {
                    console.log(`  Test Condition ${idx + 1}:`, tc);
                });
            }
            
            // Add corresponding condition row with loaded test conditions for this specific step
            const conditionRow = createConditionRow(step.id, testConditions);
            conditionRow.setAttribute('data-step-id', step.id); // Ensure row is linked to step
            conditionRow.setAttribute('data-order', step.order); // Store order for later use
            conditions.appendChild(conditionRow);
            
            console.log(`Added condition row for step ${step.id} with ${testConditions.length} conditions (Order: ${step.order})`);
        }
        
        // Update timeline after all steps are added
        setTimeout(() => {
            updateTimeline();
            updateConditionsHeaderVisibility();
        }, 200);
    }
    
    function createStepCard(step, index) {
        const wrapper = document.createElement('div');
        wrapper.className = 'ul-card-wrapper';
        wrapper.setAttribute('data-step-id', step.id);

        // Function to get user-friendly action type display name
        function getActionTypeDisplayName(actionType) {
            const actionTypeMap = {
                'Click': 'Click',
                'SetValue': 'Set Value',
                'Listen': 'Listen for events',
                'OpenListItem': 'Open listed item'
            };
            return actionTypeMap[actionType] || actionType || 'Action';
        }

        const actionTypeDisplay = getActionTypeDisplayName(step.actionType);
        const subtitle = step.target || step.description || 'Click to edit';

        const div = document.createElement('div');
        div.className = 'ul-card';
        div.innerHTML = `
            <div class="ul-card-content" onclick="openStepDetails('${step.id}')">
                ${actionTypeDisplay}<br>
                <small>${subtitle}</small>
            </div>
            <div class="ul-menu-dots-container">
                <div class="ul-menu-dots">• •
                    <div class="ul-menu-dropdown">
                        <div class="ul-menu-item" onclick="openStepDetails('${step.id}')">Edit</div>
                        <div class="ul-menu-item" onclick="viewStepActions('${step.id}')">Actions</div>
                        <div class="ul-menu-item" onclick="deleteStep('${step.id}')">Delete</div>
                    </div>
                </div>
            </div>
        `;

        wrapper.appendChild(div);
        return wrapper;
    }
    
    function openStepDetails(stepId) {
        Page.setMainPage(`/Admin/Userlane/Step/${stepId}`);
        Page.setInfo(`/Admin/Userlane/Step/${stepId}`);
        Page.load(`/Admin/Userlane/Step/${stepId}`, {}, {getRootNode: document.querySelector('main')});
    }
    
    function viewStepActions(stepId) {
        Page.setMainPage(`/Admin/Userlane/Step/${stepId}/Actions`);
        Page.setInfo(`/Admin/Userlane/Step/${stepId}/Actions`);
        Page.load(`/Admin/Userlane/Step/${stepId}`, {}, {getRootNode: document.querySelector('main')});
    }
    
    async function deleteStep(stepId) {
        if (confirm('Are you sure you want to delete this step?')) {
            try {
                const response = await fetch(`/Api/UserlaneStep/${stepId}`, {
                    method: 'DELETE'
                });
                
                if (response.ok) {
                    loadSteps(); // Reload steps
                } else {
                    alert('Error deleting step');
                }
            } catch (error) {
                console.error('Error deleting step:', error);
                alert('Error deleting step');
            }
        }
    }

    /* expose to other scripts if needed */
    window.UserlaneVis = { addRow };
    
    // Expose other functions that might be called from inline onclick handlers
    window.openCreateStepPanel = openCreateStepPanel;
    window.openStepDetails = openStepDetails;
    window.viewStepActions = viewStepActions;
    window.deleteStep = deleteStep;
    window.editTestCondition = editTestCondition;
    window.deleteTestCondition = deleteTestCondition;
    window.deleteConditionCard = deleteConditionCard;
    window.openCreateTestConditionPanel = openCreateTestConditionPanel;
    
    // Initialize timeline on page load
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize zoom handler
        zoomHandler = new ZoomHandler();
        
        setTimeout(() => {
            loadSteps();
            updateTimeline();
            updateConditionsHeaderVisibility();
        }, 200);
    });
    
    // Reload steps when a new step is created
    const createPanel = document.getElementById('create-panel');
    if (createPanel) {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'open') {
                    if (createPanel.hasAttribute('open')) {
                        // Panel was opened, set the order field if we have a pending order
                        setTimeout(() => {
                            if (window.pendingStepOrder !== undefined) {
                                console.log(`📊 Setting order field to: ${window.pendingStepOrder}`);
                                const orderInput = createPanel.querySelector('input[name="order"]');
                                if (orderInput) {
                                    orderInput.value = window.pendingStepOrder;
                                    orderInput.dispatchEvent(new Event('change'));
                                    console.log('✅ Order field set successfully');
                                } else {
                                    console.warn('❌ Order input field not found in create panel');
                                }
                                // Clear the pending order
                                delete window.pendingStepOrder;
                            }
                        }, 100);
                    } else {
                        // Panel was closed, reload steps
                        setTimeout(loadSteps, 500);
                    }
                }
            });
        });
        observer.observe(createPanel, { attributes: true });
    }

    // Test Condition Panel Functions
    async function openCreateTestConditionPanel(stepId) {
        console.log('Opening test condition panel for step:', stepId);
        
        try {
            // Use the new CreatePartial endpoint that returns just the form without layout
            const createUrl = `/Admin/Userlane/Step/${stepId}/userlaneStepTestCondition/CreatePartial`;
            console.log('Test condition URL:', createUrl);
            
            // Test if the URL works by fetching it first
            const testResponse = await fetch(createUrl);
            console.log('Test response status:', testResponse.status);
            if (!testResponse.ok) {
                console.error('Test condition URL failed:', testResponse.status, testResponse.statusText);
                const responseText = await testResponse.text();
                console.error('Response text:', responseText);
                return;
            }
            
            await Page.showCreatePanel(createUrl, {userlaneStepId: stepId}, 'Create Test Condition');
            Page.setPanelInfo(createUrl, {userlaneStepId: stepId}, 'Create Test Condition');
            
            // Set up the form after it loads with a longer delay
            setTimeout(() => {
                const form = Page.createSlideOut.querySelector('lvl-form, .form');
                console.log('Found form:', form);
                if (form) {
                    const parentInput = form.querySelector('input[name="userlaneStepId"]');
                    console.log('Found parent input:', parentInput);
                    if (parentInput) {
                        parentInput.value = stepId;
                        parentInput.dispatchEvent(new Event('change'));
                    }
                    if (form.tagName === 'LVL-FORM') {
                        form.skeleton = false;
                    }
                    
                    // Override the save button to use the correct API endpoint for test conditions
                    const saveButton = Page.createSlideOut.querySelector('[data-action="save"]');
                    if (saveButton) {
                        // Remove existing event listeners
                        const newSaveButton = saveButton.cloneNode(true);
                        saveButton.parentNode.replaceChild(newSaveButton, saveButton);
                        
                        // Add new event listener for test condition save
                        newSaveButton.addEventListener('click', async () => {
                            console.log('Saving test condition...');
                            const result = await Form.storeData(form, '/Api/UserlaneStepTestCondition', 'POST');
                            if (result) {
                                Page.createSlideOut.open = false;
                                // Reload steps after successful save
                                loadSteps();
                            }
                        });
                    }
                } else {
                    console.error('No form found in slide out. Available elements:', 
                        Page.createSlideOut.innerHTML);
                }
            }, 500);
            
        } catch (error) {
            console.error('Error loading test condition form:', error);
            alert('Error loading test condition form');
        }
    }

})(); // Close namespace wrapper
</script>