using System.Net.Mime;
using System.Text.Json;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.Localization;
using Levelbuild.Core.FrontendDtos.User;
using Levelbuild.Entities;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Mvc.PublicApi;
using Levelbuild.Frontend.WebApp.Features.PublicApi.Attributes;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Swashbuckle.AspNetCore.Annotations;

namespace Levelbuild.Frontend.WebApp.Features.PublicApi.Controllers;

/// <summary>
/// Controller handling user prefrences.
/// </summary>
[SwaggerGroup("App Configuration", 1)]
[Produces(MediaTypeNames.Application.Json)]
[Consumes(MediaTypeNames.Application.Json)]
[Route("PublicApi/Preferences/")]
public class UserPreferencesController : PublicApiController
{
	private readonly UserManager _userManager;

	/// <inheritdoc />
	public UserPreferencesController(ILogManager logManager, IDbContextFactory<CoreDatabaseContext> contextFactory, UserManager userManager, IVersionReader versionReader) : base(
		logManager.GetLoggerForClass<UserPreferencesController>(), contextFactory, versionReader)
	{
		_userManager = userManager;
	}
	
	/// <summary>
	/// Returns current User's preferences.
	/// </summary>
	/// <returns></returns>
	[HttpGet()]
	[SwaggerOperation("Get preferences of current user")]
	[SwaggerResponse(StatusCodes.Status200OK, Type = typeof(PublicApiResponse<UserPreferencesDto>))]
	[SwaggerResponse(StatusCodes.Status400BadRequest, Type = typeof(PublicApiResponse))]
	[SwaggerResponse(StatusCodes.Status404NotFound, Type = typeof(PublicApiResponse))]
	public async Task<ActionResult<PublicApiResponse>> Get()
	{
		try
		{
			var preferences = await _userManager.GetCurrentUserPreferencesAsync();
			if (preferences == null)
				return GetNotFoundResponse($"Preferences for user: '{(await _userManager.GetCurrentUserAsync())?.DisplayName}' could not be found");
			
			var json = JsonSerializer.SerializeToElement(preferences, SerializerOptions);
			return GetOkResponse(json);
		}
		catch (Exception e)
		{
			Logger.Error(e, "User preferences could not be loaded");
			return GetBadRequestResponse("User preferences could not be loaded.");
		}
	}
	
	/// <summary>
	/// Lists available locales.
	/// </summary>
	/// <returns></returns>
	[HttpGet("Locales")]
	[SwaggerOperation("Lists all available locales")]
	[SwaggerResponse(StatusCodes.Status200OK, Type = typeof(PublicApiResponse<IList<CultureDto>>))]
	[SwaggerResponse(StatusCodes.Status400BadRequest, Type = typeof(PublicApiResponse))]
	public ActionResult<PublicApiResponse> ListLocales()
	{
		try
		{
			var json = JsonSerializer.SerializeToElement(DatabaseContext.Cultures.ToList(), SerializerOptions);
			return GetOkResponse(json);
		}
		catch (Exception e)
		{
			Logger.Error(e, "Culture list could not be loaded");
			return GetBadRequestResponse("Culture list could not be loaded.");
		}
	}
}