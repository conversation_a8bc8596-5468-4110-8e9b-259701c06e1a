using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Userlane;
using Levelbuild.Frontend.WebApp.Features.ViewManagement;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions;

#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Frontend.WebApp.Features.UserlaneStep.ViewModels;

[ExcludeFromCodeCoverage]
public class UserlaneStepForm(ViewType viewType = ViewType.Create)
{
	public ViewType ViewType { get; init; } = viewType;
	public UserlaneStepDto? UserlaneStep { get; init; }

	public Guid? UserlaneId { get; set; }

	public List<UserlaneStepActionType> UserlaneStepActionTypes { get; } = Enum.GetValues(typeof(UserlaneStepActionType))
		.Cast<UserlaneStepActionType>()
		.ToList();

	public List<AutocompleteOptionDefinition> DataFields { get; set; } = [];
}