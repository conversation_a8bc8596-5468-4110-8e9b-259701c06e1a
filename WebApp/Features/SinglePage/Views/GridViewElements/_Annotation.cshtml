@using Levelbuild.Core.EntityInterface
@using Levelbuild.Frontend.WebApp.Features.ViewManagement
@model Levelbuild.Frontend.WebApp.Features.SinglePage.ViewModels.GridViewFieldModel
@inject IExtendedStringLocalizerFactory LocalizerFactory

@{
	var viewerLocalizer = LocalizerFactory.Create("Components/Viewer", "");
	var blueprintViewerId = Guid.NewGuid();
}
<span class="grid-element-wrapper skeleton__block" style="grid-row-start: @Model.Field.RowStart; grid-row-end: @Model.Field.RowEnd; grid-column-start: @Model.Field.ColStart; grid-column-end: @Model.Field.ColEnd;">
	<lvl-blueprint id="@(blueprintViewerId)" overlay-action="@(Model.ViewType == ViewType.Create ? "" : Model.TextView == true ? "open" : "edit")" preview element-hint-icon="map"></lvl-blueprint>
	<script type="module" defer>
		const blueprintViewer = document.getElementById('@(blueprintViewerId)')
		blueprintViewer.addEventListener('preview:edit', () => {
			
			let editDialog = document.createElement('lvl-dialog')
			editDialog.id = `annotation-@(blueprintViewerId)-edit`
			editDialog.width = 'clamp(60vw, 1128px, 80vw)'
			editDialog.modal = true
			editDialog.hideHeader = true
			editDialog.hideFooter= true
			editDialog.ignoreOverflow = true
			editDialog.customKeydownHandler = true
			
			// append blueprint viewer
			let blueprintContainer = document.createElement('div')
			blueprintContainer.style.height = 'calc(100vh - 120px)'
			editDialog.append(blueprintContainer)
			
			let editViewer = document.createElement('lvl-blueprint')
			editViewer.id = `annotation-@(blueprintViewerId)-viewer`
			editViewer.url = blueprintViewer.url
			editViewer.imageWidth = blueprintViewer.imageWidth
			editViewer.imageHeight = blueprintViewer.imageHeight
			editViewer.previewTarget = { ...blueprintViewer.previewTarget }
			
			blueprintContainer.append(editViewer)
			
			// append and show dialog
			document.getElementById('content').append(editDialog)
			editDialog.showOnce()
			
			editViewer.addEventListener('preview:close', () => {
				editDialog.closeDialog()
			})

			editViewer.addEventListener('preview:commit', async (event) => {
				let section = document.getElementById('@(Model.Field.SectionId)')
				let form = section.closest('lvl-form')
				
				@if (Model.ViewType == ViewType.Dialog)
				{
					<text>
						if (form && event.detail) {
							// update preview inside dialog
							blueprintViewer.previewTarget = { ...blueprintViewer.previewTarget, ...event.detail }
							
							// update dialog form values
							form.setValue('AnnotationX', Math.round(event.detail.x), false, true)
							form.setValue('AnnotationY', Math.round(event.detail.y), false, true)
						}
					</text>
				}
				else
				{
					<text>											
						// update position directly
						const response = await fetch(`/Api/DataSources/@(Model.DataSourceId)/Elements/${Page.getFormData().Id}`, {
							method: 'PATCH',
							headers: { 'Content-Type': 'application/json' },
							body: JSON.stringify({
								"AnnotationX": Math.round(event.detail.x),
								"AnnotationY": Math.round(event.detail.y)
							})
						})

						// show toast if update failed 
						if (!response.ok) {
							const toaster = document.getElementById('toaster')
							toaster.notifySimple({ heading: '@(viewerLocalizer["annotationMoveFailed"])', type: 'error' })
							return
						}
				
						// inject values into current page without overwriting everything else (there may be unsaved changes)
						Page.updateFormData(form, { AnnotationX: Math.round(event.detail.x), AnnotationY: Math.round(event.detail.y) })
				
						// update pin preview
						blueprintViewer.previewTarget = { ...blueprintViewer.previewTarget, ...event.detail }
					</text>
				}
				editDialog.closeDialog()
			})
		})
		
		const updatePreview = () => {
			// update preview again if page content changes
			Page.getBlueprintChangeSignal().addEventListener('abort', async () => {
				updatePreview()
			}, { once: true, signal: Page.getPageChangeSignal() })
			
			const blueprintConfig = Page.getBlueprintConfig()
			if (!blueprintViewer || !document.contains(blueprintViewer))
				return
			
			if (!blueprintConfig) {											
				blueprintViewer.url = ''
				blueprintViewer.imageWidth = 0
				blueprintViewer.imageHeight = 0
				blueprintViewer.style.display = 'none'
				return
			}

			blueprintViewer.style.removeProperty('display')
			
			let section = document.getElementById('@(Model.Field.SectionId)')
			let form = section?.closest('lvl-form')
			if (!form)
				return
			
			const formData = form.getValues(true, true)
			const blueprintReference = Page.getFormData()['@Model.Field.DataField?.Name']
			
			if (formData['AnnotationX'] && formData['AnnotationY']) {
				let previewTarget = {x: formData['AnnotationX'], y: formData['AnnotationY']}
				
				// try to find matching annotation type (because it determines the pin icon)
				if (blueprintConfig.pinSource) {
					const typeFieldValue = formData[blueprintConfig.pinSource.typeFieldName]
					const typeFieldId = typeFieldValue != null && typeFieldValue instanceof Object ? typeFieldValue.id : typeFieldValue
					const pinSourceType = blueprintConfig.pinSource.types.find(it => it.id === typeFieldId)
					if (pinSourceType)
						previewTarget['icon'] = pinSourceType.icon
				}
				if (blueprintConfig.pinColor)
					previewTarget['color'] = blueprintConfig.pinColor

				blueprintViewer.previewTarget = previewTarget
			}

			blueprintViewer.url = `/Api/DataSources/@(Model.Field.DataField?.LookupSourceId)/Files/${blueprintConfig.imageInfo.fileId}/DeepZoom/SubFiles/`
			blueprintViewer.imageWidth = blueprintConfig.imageInfo.width
			blueprintViewer.imageHeight = blueprintConfig.imageInfo.height

			@if (Model.Field.DataField?.LookupSource?.DefaultDetailPage != null) {
				<text>
					blueprintViewer.overlayTarget = `/Public/Pages/@(Model.Field.DataField.LookupSource.DefaultDetailPage!.Slug)/${blueprintReference['id']}/overview`
					blueprintViewer.elementHint = blueprintReference['name']
				</text>
			}
		}
		updatePreview()
	</script>
</span>