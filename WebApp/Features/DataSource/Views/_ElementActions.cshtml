@using Levelbuild.Core.EntityInterface
@using Levelbuild.Frontend.WebApp.Features.ViewManagement
@using Levelbuild.Frontend.WebApp.Shared.Enums
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@model Levelbuild.Frontend.WebApp.Features.DataSource.ViewModels.DataSourceForm
@inject IExtendedStringLocalizerFactory LocalizerFactory

@{
	var localizer = LocalizerFactory.Create("DataSource", "elementActions");
}
<div class="grid--centered">
	<form-component id="data-source-form" skeleton="@(Model is { ViewType: ViewType.Edit, DataSource: null })">
		<div class="form__item">
			<input type="hidden" class="item__value" name="id" value="@Model.DataSource?.Id"/>
		</div>
		<config-section label="@localizer["sectionDefaultActions"]">
			<list-component class="section-span-all">
				<list-line-component>
					<list-line-item-component type="LineItemType.Flag">
						<icon name="star"></icon>
					</list-line-item-component>
					<list-line-item-component type="LineItemType.Primary">@localizer["favor"]</list-line-item-component>
					<list-line-item-component type="LineItemType.Secondary">
						<toggle-component name="favor" value="@(Model.DataSource?.Favor ?? true)"></toggle-component>
					</list-line-item-component>
				</list-line-component>
				<list-line-component>
					<list-line-item-component type="LineItemType.Flag">
						<icon name="trash"></icon>
					</list-line-item-component>
					<list-line-item-component type="LineItemType.Primary">@localizer["inactive"]</list-line-item-component>
					<list-line-item-component type="LineItemType.Secondary">
						<toggle-component name="inactive" value="@(Model.DataSource?.Inactive ?? true)"></toggle-component>
					</list-line-item-component>
				</list-line-component>
			</list-component>
		</config-section>
	</form-component>
</div>