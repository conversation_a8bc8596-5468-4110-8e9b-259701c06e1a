{"version": 3, "sources": ["webpack:///./src/ui/src/components/CalibrationModal/CalibrationModal.scss?c479", "webpack:///./src/ui/src/components/CalibrationModal/CalibrationModal.scss", "webpack:///./src/ui/src/helpers/evalFraction.js", "webpack:///./src/ui/src/components/CalibrationModal/CalibrationModal.js", "webpack:///./src/ui/src/components/CalibrationModal/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "fraction", "split", "numerator", "denominator", "Number", "parseMeasurementContentsByAnnotation", "annotation", "factor", "Measure", "axis", "Scale", "getLine<PERSON><PERSON>th", "numberRegex", "fractionRegex", "pureFractionRegex", "CalibrationModal", "useSelector", "state", "selectors", "isElementOpen", "DataElements", "CALIBRATION_MODAL", "isElementDisabled", "getMeasurementUnits", "shallowEqual", "isOpen", "isDisabled", "units", "dispatch", "useDispatch", "useState", "setAnnotation", "value", "setValue", "newDistance", "setNewDistance", "unitTo", "setUnitTo", "showError", "setShowError", "t", "useTranslation", "inputRef", "useRef", "useEffect", "current", "focus", "handleAnnotationsSelected", "core", "getSelectedAnnotations", "onAnnotationSelected", "annotations", "action", "addEventListener", "removeEventListener", "onAnnotationChanged", "toFixed", "mapAnnotationToKey", "annot", "parseFloat", "handleLossOfPrecision", "scale", "getNewScale", "currentDistance", "ratio", "currentScale", "closeModal", "actions", "closeElements", "onSwipedUp", "onSwipedDown", "preventDefaultTouchmoveEvent", "className", "classNames", "Modal", "open", "closed", "onMouseDown", "e", "stopPropagation", "ref", "type", "onChange", "target", "onBlur", "inputValue", "trim", "test", "whole", "isFinite", "evalFraction", "number", "to", "map", "unit", "key", "<PERSON><PERSON>", "dataElement", "label", "onClick", "newScale", "accurateNewScale", "setAnnotationStyles", "setToolStyles", "disabled"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,ggPAAigP,KAG1hP0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,4wCCVR,eAACC,GACd,IAAoD,IAAnBA,EAASC,MAAM,KAAI,GAA7CC,EAAS,KAAEC,EAAW,KAC7B,OAAOC,OAAOF,GAAaE,OAAOD,I,qkCCcpC,IAAME,EAAuC,SAACC,GAC5C,IAAMC,EAASD,EAAWE,QAAQC,KAAK,GAAGF,OAG1C,OAFaD,EAAWI,MAAM,GAAG,IAG/B,IAAK,QACH,OAAQJ,EAAWK,gBAAkBJ,EAAS,GAChD,IAAK,KACL,QACE,OAAQD,EAAWK,gBAAkBJ,IAIrCK,EAAc,gBACdC,EAAgB,mBAChBC,EAAoB,eAyNXC,EAvNU,WACvB,IAOC,IAPmCC,aAClC,SAACC,GAAK,MAAK,CACTC,IAAUC,cAAcF,EAAOG,IAAaC,mBAC5CH,IAAUI,kBAAkBL,EAAOG,IAAaC,mBAChDH,IAAUK,oBAAoBN,MAEhCO,KACD,GAPMC,EAAM,KAAEC,EAAU,KAAEC,EAAK,KAQ1BC,EAAWC,cACiC,IAAdC,mBAAS,MAAK,GAA3CxB,EAAU,KAAEyB,EAAa,KACM,IAAZD,mBAAS,IAAG,GAA/BE,EAAK,KAAEC,EAAQ,KAC2B,IAAXH,mBAAS,GAAE,GAA1CI,EAAW,KAAEC,EAAc,KACM,IAAZL,mBAAS,IAAG,GAAjCM,EAAM,KAAEC,EAAS,KACyB,IAAfP,oBAAS,GAAM,GAA1CQ,EAAS,KAAEC,EAAY,KACvBC,EAAqB,EAAhBC,cAAgB,GAApB,GACFC,EAAWC,iBAAO,MAExBC,qBAAU,WAAM,MACdnB,IAAUiB,SAAiB,QAAT,EAARA,EAAUG,eAAO,OAAjB,EAAmBC,SAC7BC,EAA0BC,IAAKC,4BAC9B,CAACxB,IAEJmB,qBAAU,WACR,IAAMM,EAAuB,SAACC,EAAaC,GAC1B,aAAXA,EACFL,EAA0BI,GACN,eAAXC,IACTrB,EAAc,MACdE,EAAS,IACTI,EAAU,IACVF,EAAe,KAMnB,OAFAa,IAAKK,iBAAiB,qBAAsBH,GAErC,kBAAMF,IAAKM,oBAAoB,qBAAsBJ,MAC3D,IAEHN,qBAAU,WACR,IAAMW,EAAsB,SAACJ,EAAaC,GAE3B,WAAXA,GACuB,IAAvBD,EAAYrE,QACZqE,EAAY,KAAO7C,IAEnB2B,EAAS5B,EAAqCC,GAAYkD,QAAQ,IAClEnB,EAAU/B,EAAWI,MAAM,GAAG,MAKlC,OADAsC,IAAKK,iBAAiB,oBAAqBE,GACpC,kBAAMP,IAAKM,oBAAoB,oBAAqBC,MAC1D,CAACjD,IAEJ,IAAMyC,EAA4B,SAACI,GACjC,GAC0B,KAAxBA,aAAW,EAAXA,EAAarE,SAC0B,wBAAvC2E,YAAmBN,EAAY,IAC/B,CACA,IAAMO,EAAQP,EAAY,GAC1BpB,EAAc2B,GACd,IAAM1B,EAAQ3B,EAAqCqD,GAAOF,QAAQ,GAClEvB,EAASD,GACTK,EAAUqB,EAAMhD,MAAM,GAAG,IAGzByB,EAAewB,WAAW3B,MAgExB4B,EAAwB,SAACC,GAM7B,OAFAvD,EAAWI,MAAQmD,EAEZC,KAGHA,EAAc,WAClB,IAAMC,EAAkB1D,EAAqCC,GACvD0D,EAAQ9B,EAAc6B,EAEtBE,EAAe3D,EAAWI,MAMhC,MALiB,CACf,CAACuD,EAAa,GAAG,GAAIA,EAAa,GAAG,IACrC,CAACA,EAAa,GAAG,GAAKD,EAAO5B,KAM3B8B,EAAa,WACjBtC,EAASuC,IAAQC,cAAc,CAAChD,IAAaC,sBAE/C,OAAOK,IAAepB,EAAa,KACjC,kBAAC,IAAS,CACR+D,WAAYH,EACZI,aAAcJ,EACdK,8BAA4B,GAE5B,yBACEC,UAAWC,IAAW,CACpBC,OAAO,EACP3D,kBAAkB,EAClB4D,KAAMlD,EACNmD,QAASnD,IAEXoD,YAAaX,GACd,IAAC,yBAAKM,UAAU,YAAYK,YAAa,SAACC,GAAC,OAAKA,EAAEC,oBAC/C,yBAAKP,UAAU,oBACf,yBAAKA,UAAU,uBACZhC,EAAE,0BAEL,yBAAKgC,UAAU,qBACb,6BAAMhC,EAAE,6BACR,yBAAKgC,UAAU,sBACb,2BACEA,UAAWlC,EAAY,QAAU,GACjC0C,IAAKtC,EACLuC,KAAK,OACLjD,MAAOA,EACPkD,SAhHY,SAACJ,GACzBvC,GAAa,GACbN,EAAS6C,EAAEK,OAAOnD,QA+GNoD,OA7GQ,SAACN,GACrB,IAAMO,EAAaP,EAAEK,OAAOnD,MAAMsD,OAIlC,GAHmB,KAAfD,GACF9C,GAAa,GAEX3B,EAAY2E,KAAKF,GAEC,IADA1B,WAAW0B,IAE7BlD,EAAewB,WAAW0B,IAC1BpD,EAASoD,IAET9C,GAAa,QAEV,GAAI1B,EAAc0E,KAAKF,GAAa,CACzC,IAA+C,IAArBA,EAAWpF,MAAM,KAAI,GAAxCuF,EAAK,KAAExF,EAAQ,KACtB,GAAII,OAAOqF,SAASC,EAAa1F,IAAY,CAC3C,IAAM2F,EAASvF,OAAOoF,GAASE,EAAa1F,GAC5CmC,EAAewB,WAAWgC,IAC1B1D,EAAS0D,QAETpD,GAAa,QAEV,GAAIzB,EAAkByE,KAAKF,GAChC,GAAIjF,OAAOqF,SAASC,EAAaL,IAAc,CAC7C,IAAMM,EAASD,EAAaL,GAC5BlD,EAAewB,WAAWgC,IAC1B1D,EAAS0D,QAETpD,GAAa,QAGfA,GAAa,MAgFL,4BACEiC,UAAU,cACVxC,MAAOI,EACP8C,SA/Ea,SAACJ,GAC1BzC,EAAUyC,EAAEK,OAAOnD,SAgFNL,EAAMiE,GAAGC,KAAI,SAACC,GAAI,OACjB,4BAAQC,IAAKD,EAAM9D,MAAO8D,GACvBA,QAKRxD,EAAY,yBAAKkC,UAAU,oBAAoBhC,EAAE,kCAA0C,MAE9F,yBAAKgC,UAAU,uBACb,kBAACwB,EAAA,EAAM,CACLC,YAAY,uBACZC,MAAO1D,EAAE,gBACT2D,QA3FQ,WAClB,IAAMC,EAAWtC,IACXuC,EAAmBzC,EAAsBwC,GAE/CpD,IAAKsD,oBAAoBhG,EAAY,CACnCI,MAAO2F,IAITE,YACE,sCACA,QACAF,GAGFzE,EAASuC,IAAQC,cAAc,CAAChD,IAAaC,sBA6EnCmF,SAAUlE,SC7OTvB", "file": "chunks/chunk.58.js", "sourcesContent": ["var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./CalibrationModal.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".open.CalibrationModal{visibility:visible}.closed.CalibrationModal{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.CalibrationModal .footer .modal-button.confirm:hover{background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.CalibrationModal .footer .modal-button.confirm{background:var(--primary-button);border-color:var(--primary-button);color:var(--primary-button-text)}.CalibrationModal .footer .modal-button.confirm.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.CalibrationModal .footer .modal-button.confirm.disabled span{color:var(--primary-button-text)}.CalibrationModal .footer .modal-button.cancel:hover,.CalibrationModal .footer .modal-button.secondary-btn-custom:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6)}.CalibrationModal .footer .modal-button.cancel,.CalibrationModal .footer .modal-button.secondary-btn-custom{border:none;box-shadow:inset 0 0 0 1px var(--primary-button);color:var(--primary-button)}.CalibrationModal .footer .modal-button.cancel.disabled,.CalibrationModal .footer .modal-button.secondary-btn-custom.disabled{cursor:default;border:none;box-shadow:inset 0 0 0 1px rgba(43,115,171,.5);color:rgba(43,115,171,.5)}.CalibrationModal .footer .modal-button.cancel.disabled span,.CalibrationModal .footer .modal-button.secondary-btn-custom.disabled span{color:rgba(43,115,171,.5)}.CalibrationModal{position:fixed;left:0;bottom:0;z-index:100;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:var(--modal-negative-space)}.CalibrationModal .modal-container .wrapper .modal-content{padding:10px}.CalibrationModal .footer{display:flex;flex-direction:row;justify-content:flex-end;width:100%;margin-top:13px}.CalibrationModal .footer.modal-footer{padding:16px;margin:0;border-top:1px solid var(--divider)}.CalibrationModal .footer .modal-button{display:flex;justify-content:center;align-items:center;padding:6px 18px;margin:8px 0 0;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:30px;cursor:pointer}.CalibrationModal .footer .modal-button.confirm{margin-left:4px}.CalibrationModal .footer .modal-button.secondary-btn-custom{border-radius:4px;padding:2px 20px 4px;cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .CalibrationModal .footer .modal-button{padding:23px 8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .CalibrationModal .footer .modal-button{padding:23px 8px}}.CalibrationModal .swipe-indicator{background:var(--swipe-indicator-bg);border-radius:2px;height:4px;width:38px;position:absolute;top:12px;margin-left:auto;margin-right:auto;left:0;right:0}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .CalibrationModal .swipe-indicator{display:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .CalibrationModal .swipe-indicator{display:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .CalibrationModal .swipe-indicator{width:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .CalibrationModal .swipe-indicator{width:32px}}.CalibrationModal .container{border-radius:4px;overflow-y:auto;max-height:100%}@media(max-height:500px){.App:not(.is-web-component) .CalibrationModal .container,.CalibrationModal .App:not(.is-web-component) .container{overflow:auto;max-height:100%}}@container (max-height: 500px){.App.is-web-component .CalibrationModal .container,.CalibrationModal .App.is-web-component .container{overflow:auto;max-height:100%}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .CalibrationModal .container,.CalibrationModal .App:not(.is-in-desktop-only-mode):not(.is-web-component) .container{width:100%;position:fixed;left:0;bottom:0;padding-top:4px;min-width:100px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .CalibrationModal .container,.CalibrationModal .App.is-web-component:not(.is-in-desktop-only-mode) .container{width:100%;position:fixed;left:0;bottom:0;padding-top:4px;min-width:100px}}.CalibrationModal .container{display:flex;flex-direction:column;min-width:400px;box-shadow:0 0 3px 0 var(--document-box-shadow);padding:8px;background:var(--component-background)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .CalibrationModal .container{padding:24px 24px 16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .CalibrationModal .container{padding:24px 24px 16px}}.CalibrationModal .container .calibration__header{font-size:16px;margin-top:8px;margin-bottom:8px}.CalibrationModal .container .calibration__body{padding-top:8px}.CalibrationModal .container .calibration__body .calibration__input{margin-top:8px;display:flex;flex-direction:row;justify-content:space-between}.CalibrationModal .container .calibration__body input{width:100%;height:30px;font-size:13px;border:1px solid var(--border);border-radius:4px;color:var(--text-color);padding:4px 42px 6px 8px;margin-right:8px}.CalibrationModal .container .calibration__body input:focus{outline:none;border:1px solid var(--focus-border)}.CalibrationModal .container .calibration__body input::-moz-placeholder{color:var(--placeholder-text)}.CalibrationModal .container .calibration__body input::placeholder{color:var(--placeholder-text)}.CalibrationModal .container .calibration__body .error{border:1px solid var(--error-border-color)}.CalibrationModal .container .calibration__body .unitToInput{height:29px;width:45px;border-radius:5px;border:1px solid var(--border);font-size:13px}.CalibrationModal .container .calibration__body .unitToInput:focus{outline:none;border:1px solid var(--focus-border)}.CalibrationModal .container .calibration__body .unitToInput::-moz-placeholder{color:var(--placeholder-text)}.CalibrationModal .container .calibration__body .unitToInput::placeholder{color:var(--placeholder-text)}.CalibrationModal .container .calibration__body .errorMeasurement{font-size:13px;margin-top:8px;margin-bottom:8px;color:var(--error-text-color);max-width:400px}.CalibrationModal .container .calibration__footer{display:flex;justify-content:flex-end;margin-top:8px}.CalibrationModal .container .calibration__footer .Button{display:flex;justify-content:center;align-items:center;color:var(--primary-button-text);font-weight:600;padding:6px 18px;margin-top:8px;margin-left:5px;width:auto;width:-moz-fit-content;width:fit-content;background:var(--primary-button);border-radius:4px;height:30px;cursor:pointer}.CalibrationModal .container .calibration__footer .Button:hover{background:var(--primary-button-hover)}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "export default (fraction) => {\n  const [numerator, denominator] = fraction.split('/');\n  return Number(numerator) / Number(denominator);\n};\n", "import React, { useState, useEffect, useRef } from 'react';\nimport classNames from 'classnames';\nimport { useSelector, useDispatch, shallowEqual } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\nimport Button from 'components/Button';\nimport core from 'core';\nimport { mapAnnotationToKey } from 'constants/map';\nimport setToolStyles from 'helpers/setToolStyles';\nimport evalFraction from 'helpers/evalFraction';\nimport actions from 'actions';\nimport selectors from 'selectors';\nimport { Swipeable } from 'react-swipeable';\nimport DataElements from 'constants/dataElement';\n\nimport './CalibrationModal.scss';\n\nconst parseMeasurementContentsByAnnotation = (annotation) => {\n  const factor = annotation.Measure.axis[0].factor;\n  const unit = annotation.Scale[1][1];\n\n  switch (unit) {\n    case 'ft-in':\n      return (annotation.getLineLength() * factor / 12);\n    case 'in':\n    default:\n      return (annotation.getLineLength() * factor);\n  }\n};\n\nconst numberRegex = /^\\d*(\\.\\d*)?$/;\nconst fractionRegex = /^\\d*(\\s\\d\\/\\d*)$/;\nconst pureFractionRegex = /^(\\d\\/\\d*)*$/;\n\nconst CalibrationModal = () => {\n  const [isOpen, isDisabled, units] = useSelector(\n    (state) => [\n      selectors.isElementOpen(state, DataElements.CALIBRATION_MODAL),\n      selectors.isElementDisabled(state, DataElements.CALIBRATION_MODAL),\n      selectors.getMeasurementUnits(state),\n    ],\n    shallowEqual\n  );\n  const dispatch = useDispatch();\n  const [annotation, setAnnotation] = useState(null);\n  const [value, setValue] = useState('');\n  const [newDistance, setNewDistance] = useState(0);\n  const [unitTo, setUnitTo] = useState('');\n  const [showError, setShowError] = useState(false);\n  const [t] = useTranslation();\n  const inputRef = useRef(null);\n\n  useEffect(() => {\n    isOpen && inputRef?.current?.focus();\n    handleAnnotationsSelected(core.getSelectedAnnotations());\n  }, [isOpen]);\n\n  useEffect(() => {\n    const onAnnotationSelected = (annotations, action) => {\n      if (action === 'selected') {\n        handleAnnotationsSelected(annotations);\n      } else if (action === 'deselected') {\n        setAnnotation(null);\n        setValue('');\n        setUnitTo('');\n        setNewDistance(0);\n      }\n    };\n\n    core.addEventListener('annotationSelected', onAnnotationSelected);\n\n    return () => core.removeEventListener('annotationSelected', onAnnotationSelected);\n  }, []);\n\n  useEffect(() => {\n    const onAnnotationChanged = (annotations, action) => {\n      if (\n        action === 'modify' &&\n        annotations.length === 1 &&\n        annotations[0] === annotation\n      ) {\n        setValue(parseMeasurementContentsByAnnotation(annotation).toFixed(2));\n        setUnitTo(annotation.Scale[1][1]);\n      }\n    };\n\n    core.addEventListener('annotationChanged', onAnnotationChanged);\n    return () => core.removeEventListener('annotationChanged', onAnnotationChanged);\n  }, [annotation]);\n\n  const handleAnnotationsSelected = (annotations) => {\n    if (\n      annotations?.length === 1 &&\n      mapAnnotationToKey(annotations[0]) === 'distanceMeasurement'\n    ) {\n      const annot = annotations[0];\n      setAnnotation(annot);\n      const value = parseMeasurementContentsByAnnotation(annot).toFixed(2);\n      setValue(value);\n      setUnitTo(annot.Scale[1][1]);\n      // initial new distance should be the same as the value\n      // in case the user doesn't change the input value\n      setNewDistance(parseFloat(value));\n    }\n  };\n\n  const handleInputChange = (e) => {\n    setShowError(false);\n    setValue(e.target.value);\n  };\n  const validateInput = (e) => {\n    const inputValue = e.target.value.trim();\n    if (inputValue === '') {\n      setShowError(true);\n    }\n    if (numberRegex.test(inputValue)) {\n      const newDistance = parseFloat(inputValue);\n      if (newDistance !== 0) {\n        setNewDistance(parseFloat(inputValue));\n        setValue(inputValue);\n      } else {\n        setShowError(true);\n      }\n    } else if (fractionRegex.test(inputValue)) {\n      const [whole, fraction] = inputValue.split(' ');\n      if (Number.isFinite(evalFraction(fraction))) {\n        const number = Number(whole) + evalFraction(fraction);\n        setNewDistance(parseFloat(number));\n        setValue(number);\n      } else {\n        setShowError(true);\n      }\n    } else if (pureFractionRegex.test(inputValue)) {\n      if (Number.isFinite(evalFraction(inputValue))) {\n        const number = evalFraction(inputValue);\n        setNewDistance(parseFloat(number));\n        setValue(number);\n      } else {\n        setShowError(true);\n      }\n    } else {\n      setShowError(true);\n    }\n  };\n\n  const handleSelectChange = (e) => {\n    setUnitTo(e.target.value);\n  };\n  const handleApply = () => {\n    const newScale = getNewScale();\n    const accurateNewScale = handleLossOfPrecision(newScale);\n\n    core.setAnnotationStyles(annotation, {\n      Scale: accurateNewScale,\n    });\n\n    // this will also set the Scale for the other two measurement tools\n    setToolStyles(\n      'AnnotationCreateDistanceMeasurement',\n      'Scale',\n      accurateNewScale\n    );\n\n    dispatch(actions.closeElements([DataElements.CALIBRATION_MODAL]));\n  };\n\n  const handleLossOfPrecision = (scale) => {\n    // when the new distance that's entered in the modal is much bigger than the current distance, loss of precision can happen\n    // because internally WebViewer will do several multiplications and divisions to get the value to store in a measure dictionary\n    // in this case, setting 'Scale' again should fix this issue because this time the new distance and the current distance is very close, and we should get the accurate scale\n    annotation.Scale = scale;\n\n    return getNewScale();\n  };\n\n  const getNewScale = () => {\n    const currentDistance = parseMeasurementContentsByAnnotation(annotation);\n    const ratio = newDistance / currentDistance;\n\n    const currentScale = annotation.Scale;\n    const newScale = [\n      [currentScale[0][0], currentScale[0][1]],\n      [currentScale[1][0] * ratio, unitTo],\n    ];\n\n    return newScale;\n  };\n\n  const closeModal = () => {\n    dispatch(actions.closeElements([DataElements.CALIBRATION_MODAL]));\n  };\n  return isDisabled || !annotation ? null : (\n    <Swipeable\n      onSwipedUp={closeModal}\n      onSwipedDown={closeModal}\n      preventDefaultTouchmoveEvent\n    >\n      <div\n        className={classNames({\n          Modal: true,\n          CalibrationModal: true,\n          open: isOpen,\n          closed: !isOpen,\n        })}\n        onMouseDown={closeModal}\n      > <div className=\"container\" onMouseDown={(e) => e.stopPropagation()}>\n          <div className=\"swipe-indicator\" />\n          <div className=\"calibration__header\">\n            {t('component.calibration')}\n          </div>\n          <div className=\"calibration__body\">\n            <div>{t('message.enterMeasurement')}</div>\n            <div className=\"calibration__input\">\n              <input\n                className={showError ? 'error' : ''}\n                ref={inputRef}\n                type=\"text\"\n                value={value}\n                onChange={handleInputChange}\n                onBlur={validateInput}\n              />\n              <select\n                className=\"unitToInput\"\n                value={unitTo}\n                onChange={handleSelectChange}\n              >\n                {units.to.map((unit) => (\n                  <option key={unit} value={unit}>\n                    {unit}\n                  </option>\n                ))}\n              </select>\n            </div>\n            {showError ? <div className=\"errorMeasurement\">{t('message.errorEnterMeasurement')}</div> : null}\n          </div>\n          <div className=\"calibration__footer\">\n            <Button\n              dataElement=\"passwordSubmitButton\"\n              label={t('action.apply')}\n              onClick={handleApply}\n              disabled={showError}\n            />\n          </div>\n        </div>\n      </div>\n    </Swipeable>\n  );\n};\n\nexport default CalibrationModal;\n", "import CalibrationModal from './CalibrationModal';\n\nexport default CalibrationModal;"], "sourceRoot": ""}