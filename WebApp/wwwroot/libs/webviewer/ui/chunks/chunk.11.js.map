{"version": 3, "sources": ["webpack:///./src/ui/src/helpers/handleFreeTextAutoSizeToggle.js"], "names": ["annotation", "setAutoSizeFont", "isAutoSizeFont", "freeTextAnnot", "calculatedFontSize", "getCalculatedFontSize", "FontSize", "switchToAutoFontSize", "core", "getAnnotationManager", "redrawAnnotation"], "mappings": "4FAAA,WASe,aAACA,EAAYC,EAAiBC,GAC3C,IAAMC,EAAgBH,EAChBI,EAAqBD,EAAcE,wBACrCH,EACFC,EAAcG,SAAWF,EAEzBD,EAAcI,uBAGhBN,GAAiBC,GACjBM,IAAKC,uBAAuBC,iBAAiBP", "file": "chunks/chunk.11.js", "sourcesContent": ["import core from 'core';\n\n/**\n * @ignore\n * handler for auto size font toggle\n * @param {FreeTextAnnotation} annotation annotation to toggle auto size font\n * @param {function} setAutoSizeFont function to set auto size font\n * @param {boolean} isAutoSizeFont current auto size font value\n */\nexport default (annotation, setAutoSizeFont, isAutoSizeFont) => {\n  const freeTextAnnot = annotation;\n  const calculatedFontSize = freeTextAnnot.getCalculatedFontSize();\n  if (isAutoSizeFont) {\n    freeTextAnnot.FontSize = calculatedFontSize;\n  } else {\n    freeTextAnnot.switchToAutoFontSize();\n  }\n\n  setAutoSizeFont(!isAutoSizeFont);\n  core.getAnnotationManager().redrawAnnotation(freeTextAnnot);\n};\n\n"], "sourceRoot": ""}