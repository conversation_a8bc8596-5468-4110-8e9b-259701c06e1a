{"action": {"addParagraph": "<PERSON><PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "addSheet": "<PERSON><PERSON><PERSON><PERSON>", "apply": "Aplicar", "applyAll": "Aplicar todos", "calendar": "<PERSON><PERSON><PERSON><PERSON>", "calibrate": "Calibrar", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "confirme", "currentPageIs": "A página atual é", "clear": "Limpar", "clearAll": "<PERSON><PERSON> tudo", "close": "<PERSON><PERSON><PERSON>", "undo": "<PERSON><PERSON><PERSON>", "redo": "pronto", "comment": "Novo comentário", "reply": "<PERSON><PERSON><PERSON><PERSON>", "copy": "Copiar", "cut": "Corte", "paste": "Colar", "pasteWithoutFormatting": "colar sem formatar", "delete": "<PERSON><PERSON><PERSON>", "deleted": "Excluído", "group": "Grupo", "ungroup": "Desagrupar", "download": "Download", "edit": "<PERSON><PERSON>", "collapse": "Colapso", "expand": "Expandir", "extract": "Extrair", "extractPage": "Extrair página", "enterFullscreen": "Tela cheia", "exitFullscreen": "<PERSON>r da tela cheia", "fit": "Ajustar", "fitToPage": "Ajustar à página", "fitToWidth": "Ajustar à largura", "more": "<PERSON><PERSON>", "openFile": "Abrir arquivo", "showMoreFiles": "<PERSON>rar mais arquivos", "page": "<PERSON><PERSON><PERSON><PERSON>", "of": "de", "pagePrev": "Página anterior", "pageNext": "Próxima página", "pageSet": "<PERSON><PERSON><PERSON>", "print": "Imprimir", "proceed": "<PERSON><PERSON><PERSON><PERSON>", "name": "Nome", "rename": "Renomear", "remove": "Remover", "ok": "OK", "rotate": "<PERSON><PERSON><PERSON>", "rotate3D": "<PERSON><PERSON><PERSON>", "rotateClockwise": "<PERSON><PERSON><PERSON>", "rotateCounterClockwise": "<PERSON><PERSON>do <PERSON>-<PERSON>", "rotatedClockwise": "girado no sentido horário", "rotatedCounterClockwise": "girado no sentido anti-horário", "rotationIs": "a rotação atual da página é", "movedToBottomOfDocument": "movido para o final do documento", "movedToTopofDocument": "movido para o topo do documento", "extracted": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON>", "post": "<PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON><PERSON>", "showMoreResults": "<PERSON>rar mais resultados", "sign": "<PERSON><PERSON><PERSON>", "style": "<PERSON><PERSON><PERSON>", "submit": "Enviar", "zoom": "Zoom", "zoomIn": "Aproximar", "zoomOut": "Afastar", "zoomSet": "Definir zoom", "zoomChanged": "O zoom atual é", "zoomControls": "Controles de zoom", "draw": "<PERSON><PERSON><PERSON>", "type": "Tipo", "upload": "Enviar documento", "link": "Link", "unlink": "Excluir link", "fileAttachmentDownload": "Baixar arquivo anexado", "prevResult": "Resultado anterior", "nextResult": "Próximo resultado", "prev": "Anterior", "next": "Próximo", "startFormEditing": "Comece a edição do formulário", "exitFormEditing": "Sair do modo de edição de formulário", "exit": "<PERSON><PERSON><PERSON>", "addOption": "Adicionar <PERSON>", "formFieldEdit": "Editar campo de formulário", "formFieldEditMode": "Editar campos de formulário", "contentEditMode": "<PERSON><PERSON>", "viewShortCutKeysFor3D": "<PERSON><PERSON>r tec<PERSON> de atalho", "markAllRead": "Marcar todas como lidas", "pageInsertion": "Inserção de página", "insertPage": "Inserção de Página", "insert": "Inserir", "pageManipulation": "Manipulação de página", "replace": "Substituir", "replacePage": "Substituir p<PERSON><PERSON>a", "modal": "modal", "isOpen": "<PERSON><PERSON> aberto", "setDestination": "Definir destino", "showLess": "mostre menos", "showMore": "...mais", "chooseFile": "Escolha um arquivo", "changeDate": "Alterar data", "browse": "<PERSON><PERSON><PERSON>", "selectYourOption": "Selecione sua opção", "open": "Abe<PERSON>o", "deselectAll": "<PERSON><PERSON><PERSON> to<PERSON>", "select": "Selecione", "moveToTop": "Mover para o topo", "moveToBottom": "Mover para o fundo", "movePageToTop": "Mover página para o topo", "movePageToBottom": "Mover a página para baixo", "moveUp": "Mover para cima", "moveDown": "Mover para baixo", "moveLeft": "Mover para a esquerda", "moveRight": "Mover para a direita", "backToMenu": "Voltar ao menu", "redactPages": "<PERSON><PERSON><PERSON>", "playAudio": "<PERSON><PERSON><PERSON><PERSON>", "pauseAudio": "pausa de á<PERSON>o", "selectAll": "Selecionar tudo", "unselect": "<PERSON><PERSON><PERSON>", "addMark": "Adicionar marca", "viewFile": "Visualizar arquivo", "multiReplyAnnotations": "Responder às anotações selecionadas ({{count}})", "comparePages": "Compara<PERSON>", "startComparison": "Iniciar compara<PERSON>", "showComparison": "Mostrar comparação", "highlightChanges": "Destacar alterações", "back": "De volta", "clearSignature": "Assinatura clara", "clearInitial": "<PERSON><PERSON> inicial", "readOnlySignature": "A assinatura somente leitura não pode ser excluída", "newDocument": "Novo Documento", "sideBySideView": "Vista lado a lado", "pageNumberInput": "Entrada do número da página", "addNewColor": "Adicionar nova cor", "deleteColor": "Excluir cor selecionada", "copySelectedColor": "Copiar cor selecionada", "showMoreColors": "Mostrar mais cores", "showLessColors": "Mostrar menos cores", "fromCustomColorPicker": "do seletor de cores personalizado", "newSpreadsheetDocument": "Nova Planilha"}, "annotation": {"areaMeasurement": "Medição de área", "arc": "Arco", "arcMeasurement": "Medição de arco", "arrow": "<PERSON><PERSON>", "callout": "Texto explicativo", "crop": "Cortar página", "caret": "<PERSON><PERSON><PERSON>", "dateFreeText": "<PERSON><PERSON><PERSON><PERSON>", "formFillCheckmark": "Carraça", "formFillCross": "<PERSON><PERSON>", "distanceMeasurement": "Medição de distância", "rectangularAreaMeasurement": "<PERSON><PERSON> retangular", "ellipseMeasurement": "<PERSON><PERSON>", "countMeasurement": "Medição de contagem", "ellipse": "Elipse", "eraser": "Apagador", "fileattachment": "Anexo", "freehand": "Desenho livre", "freeHandHighlight": "Destaque de mão livre", "freetext": "Texto livre", "markInsertText": "Inserir texto", "markReplaceText": "Substituir texto", "highlight": "Destacar", "image": "Imagem", "line": "<PERSON><PERSON>", "perimeterMeasurement": "Medição de perímetro", "polygon": "Polígono", "polygonCloud": "Nuvem", "polyline": "Pol<PERSON>nh<PERSON>", "rectangle": "Re<PERSON><PERSON><PERSON><PERSON>", "redact": "<PERSON><PERSON><PERSON><PERSON>", "formFillDot": "Ponto", "signature": "Assinatura", "snipping": "Ferramenta de Recorte", "squiggly": "<PERSON><PERSON>", "stamp": "Carimbo", "stickyNote": "<PERSON><PERSON><PERSON><PERSON>", "strikeout": "Riscar", "underline": "Sublinhar", "custom": "Personalizado", "rubberStamp": "Carimbo de borracha", "note": "<PERSON>a", "textField": "Campo de texto", "signatureFormField": "Campo de Assinatura", "checkBoxFormField": "Campo de caixa de seleção", "radioButtonFormField": "Campo do botão de rádio", "listBoxFormField": "Campo de lista de seleção", "comboBoxFormField": "Campo de caixa de combinação", "link": "Link", "other": "Outro", "3D": "3D", "sound": "Som", "changeView": "Alterar visualização", "newImage": "Nova imagem", "defaultCustomStampTitle": "Carimbo personalizado"}, "rubberStamp": {"Approved": "<PERSON><PERSON><PERSON>", "AsIs": "Como é", "Completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Confidential": "Confidencial", "Departmental": "Departamental", "Draft": "Esboço", "Experimental": "Experimental", "Expired": "<PERSON><PERSON><PERSON>", "Final": "Final", "ForComment": "Para Comentário", "ForPublicRelease": "Para divulgação pública", "InformationOnly": "Apenas informação", "NotApproved": "<PERSON>ão a<PERSON>rovado", "NotForPublicRelease": "Não para divulgação pública", "PreliminaryResults": "Resultados preliminares", "Sold": "Vendido", "TopSecret": "<PERSON><PERSON><PERSON><PERSON>", "Void": "<PERSON><PERSON><PERSON>", "SHSignHere": "Assine aqui", "SHWitness": "<PERSON><PERSON><PERSON><PERSON>", "SHInitialHere": "Inicial aqui", "SHAccepted": "<PERSON><PERSON>", "SBRejected": "<PERSON><PERSON><PERSON><PERSON>"}, "component": {"attachmentPanel": "Anexos", "leftPanel": "<PERSON><PERSON>", "toolsHeader": "Ferramentas", "searchOverlay": "Procurar", "searchPanel": "Procurar", "menuOverlay": "<PERSON><PERSON>", "notesPanel": "Anotações", "indexPanel": "<PERSON><PERSON>", "outlinePanel": "Contorno", "outlinesPanel": "Marcadores", "newOutlineTitle": "Novo título do esboço", "outlineTitle": "Título do marcador", "destination": "<PERSON><PERSON>", "bookmarkPanel": "marca páginas", "bookmarksPanel": "<PERSON><PERSON><PERSON><PERSON>", "bookmarkTitle": "Título do favorito", "bookmarkPage": "<PERSON><PERSON><PERSON><PERSON>", "signaturePanel": "Assinaturas", "layersPanel": "Camadas", "thumbnailsPanel": "Miniaturas", "toolsButton": "Ferramentas", "redaction": "Supressão", "viewControls": "Controles de visualização", "pageControls": "Controles de página", "calibration": "Calibração", "zoomOverlay": "Sobreposição de zoom", "textPopup": "<PERSON><PERSON>", "createStampButton": "Criar novo carimbo", "filter": "Filtro", "multiSelectButton": "<PERSON><PERSON><PERSON>", "pageReplaceModalTitle": "Substituir p<PERSON><PERSON>a", "files": "<PERSON>r<PERSON><PERSON>", "file": "Arquivo", "editText": "Editar texto", "redactionPanel": "Painel de redação", "tabLabel": "Aba", "noteGroupSection": {"open": "<PERSON><PERSON> <PERSON><PERSON> as anotaç<PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON> as an<PERSON><PERSON><PERSON><PERSON>"}, "comparePanel": "Painel de comparação", "watermarkPanel": "Painel de marca d'água", "mainMenu": "<PERSON><PERSON>"}, "message": {"showMore": "<PERSON><PERSON> mais", "showLess": "<PERSON><PERSON> menos", "toolsOverlayNoPresets": "Sem predefinições", "badDocument": "Falha ao carregar documento. O documento está corrompido ou é inválido.", "customPrintPlaceholder": "ex: 3, 4-10", "encryptedAttemptsExceeded": "Falha ao carregar documento criptografado. Tentativas demais.", "encryptedUserCancelled": "Falha ao carregar documento criptografado. Digitação de senha cancelada.", "enterPassword": "Esse documento é protegido por senha. Digite uma senha", "incorrectPassword": "Senha incorreta. Tentativas restantes: {{ remainingAttempts }}", "noAnnotations": "Comece a fazer anotações para deixar um comentário.", "noAnnotationsReadOnly": "Esse documento não tem anotações.", "noAnnotationsFilter": "Comece a fazer anotações e filtros aparecerão aqui.", "noBookmarks": "Nenhum marcador disponível", "noOutlines": "Esse documento não tem contorno.", "noAttachments": "Este documento não possui anexos.", "noResults": "Nenhum resultado encontrado.", "numResultsFound": "resultados encontrados", "loadError": "Erro ao carregar o documento", "notSupported": "Esse tipo de arquivo não é compatível.", "passwordRequired": "<PERSON><PERSON>", "enterPasswordPlaceholder": "Digite a senha", "preparingToPrint": "Preparando impress<PERSON>...", "annotationReplyCount": "{{count}} resposta", "annotationReplyCount_plural": "{{count}} respostas", "printTotalPageCount": "{{count}} p<PERSON><PERSON>a", "printTotalPageCount_plural": "{{count}} p<PERSON><PERSON>as", "processing": "Processando...", "searching": "Procurando...", "searchCommentsPlaceholder": "Procurar nos comentários", "searchDocumentPlaceholder": "Procurar no documento", "searchSettingsPlaceholder": "Configurações de pesquisa", "searchSuggestionsPlaceholder": "Sugestões de pesquisa", "signHere": "Assine aqui", "insertTextHere": "Insira texto aqui", "imageSignatureAcceptedFileTypes": "Somente {{acceptFileTypes}} são aceitos", "signatureRequired": "Uma assinatura e inicial são necessárias para continuar", "enterMeasurement": "Digite a medição entre os dois pontos", "errorEnterMeasurement": "O número inserido é inválido, você pode inserir valores como 7,5 ou 7 1/2", "linkURLorPage": "URL do link ou uma página", "warning": "Aviso", "svgMalicious": "Script SVG ignorado por segurança", "doNotShowAgain": "não me mostre isso novamente", "doNotAskAgain": "Não pergunte novamente", "enterReplacementText": "Digite o texto que deseja substituir", "sort": "Organizar", "sortBy": "Ordenar por", "emptyCustomStampInput": "O texto do carimbo não pode estar vazio", "unpostedComment": "<PERSON><PERSON><PERSON><PERSON>", "lockedLayer": "Camada está bloqueada", "layerVisibililtyNoChange": "A visibilidade da camada não pode ser alterada", "noLayers": "Este documento não possui camadas.", "noSignatureFields": "Este documento não possui campos de assinatura.", "untitled": "<PERSON><PERSON> tí<PERSON>lo", "selectHowToLoadFile": "Selecione como carregar seu documento", "openFileByUrl": "Abrir arquivo por URL:", "enterUrlHere": "Insira o URL aqui", "openLocalFile": "Abra o arquivo local:", "selectFile": "Selecione o arquivo", "selectPageToReplace": "<PERSON><PERSON><PERSON><PERSON> as páginas do documento que deseja substituir.", "embeddedFiles": "Arquivos incorporados", "pageNum": "<PERSON><PERSON><PERSON><PERSON>", "viewBookmark": "Ver marcador na página", "error": "Erro", "errorPageNumber": "Número de página inválido. Limite é", "errorBlankPageNumber": "Por favor especifique um número de página", "errorLoadingDocument": "Ocorreu um problema ao ler este documento e algumas páginas podem não ser exibidas. Isso indica que o documento pode estar corrompido. A contagem total de páginas é {{totalPageCount}} e o número de páginas exibidas é {{displayedPageCount}}.", "noRevisions": "Este documento não possui revisões."}, "option": {"type": {"caret": "falta", "custom": "Personalizado", "ellipse": "Elipse", "fileattachment": "Anexo de Arquivo", "freehand": "Mão <PERSON>", "callout": "<PERSON><PERSON>", "freetext": "Texto livre", "line": "<PERSON><PERSON>", "polygon": "Polígono", "polyline": "Pol<PERSON>nh<PERSON>", "rectangle": "Re<PERSON><PERSON><PERSON><PERSON>", "redact": "Redigir", "signature": "Assinatura", "stamp": "Carimbo", "stickyNote": "<PERSON><PERSON> ad<PERSON>", "highlight": "Realçar", "strikeout": "Strikeout", "underline": "<PERSON><PERSON><PERSON><PERSON>", "squiggly": "S<PERSON>ggly", "3D": "3D", "other": "<PERSON>", "initials": "Iniciais", "saved": "Salvou"}, "notesOrder": {"dropdownLabel": "Classificar lista de pedidos", "position": "Posição", "time": "<PERSON><PERSON>", "status": "Status", "author": "Autor", "type": "Tipo", "color": "Cor", "createdDate": "Data de criação", "modifiedDate": "Data modificada"}, "toolbarGroup": {"dropdownLabel": "Grupos da barra de ferramentas", "flyoutLabel": "Fita", "toolbarGroup-View": "Visualizar", "toolbarGroup-Annotate": "<PERSON><PERSON><PERSON>", "toolbarGroup-Shapes": "Formas", "toolbarGroup-Insert": "Inserir", "toolbarGroup-Measure": "Medir", "toolbarGroup-Edit": "<PERSON><PERSON>", "toolbarGroup-EditText": "Editar texto", "toolbarGroup-FillAndSign": "Preencher e assinar", "toolbarGroup-Forms": "Formulários", "toolbarGroup-Redact": "Redigir", "toolbarGroup-oe-Home": "<PERSON>r", "toolbarGroup-oe-Insert": "Inserir", "toolbarGroup-oe-Review": "<PERSON><PERSON><PERSON><PERSON>"}, "annotationColor": {"StrokeColor": "<PERSON><PERSON> <PERSON> b<PERSON>a", "FillColor": "<PERSON><PERSON> <PERSON> pre<PERSON>", "TextColor": "Cor do texto"}, "colorPalette": {"colorLabel": "Cor"}, "colorPalettePicker": {"addColor": "Adicionar nova cor", "selectColor": "Selecione a cor"}, "displayMode": {"layout": "Layout", "pageTransition": "Transição de página"}, "documentControls": {"selectTooltip": "Selecione várias páginas", "closeTooltip": "<PERSON><PERSON><PERSON> m<PERSON>"}, "bookmarkOutlineControls": {"edit": "<PERSON><PERSON>", "done": "<PERSON><PERSON>", "reorder": "Reordenar"}, "layout": {"cover": "Capa", "double": "<PERSON><PERSON><PERSON><PERSON> dupla", "single": "Página única"}, "mathSymbols": "<PERSON><PERSON><PERSON><PERSON>", "notesPanel": {"separator": {"today": "Hoje", "yesterday": "Ontem", "unknown": "Desconhecido"}, "noteContent": {"noName": "(sem nome)", "noDate": "(sem data)"}, "toggleMultiSelect": "Alternar seleção múltipla para anotação"}, "pageTransition": {"continuous": "<PERSON><PERSON><PERSON><PERSON>", "default": "<PERSON><PERSON>gin<PERSON> por página", "reader": "<PERSON><PERSON>"}, "print": {"all": "<PERSON><PERSON>", "current": "<PERSON><PERSON><PERSON> atual", "pages": "<PERSON><PERSON><PERSON><PERSON>", "specifyPages": "Especificar páginas", "view": "<PERSON><PERSON><PERSON>", "pageQuality": "Qualidade de impressão", "qualityNormal": "Normal", "qualityHigh": "Alto", "includeAnnotations": "Incluir anotações", "includeComments": "<PERSON>luir coment<PERSON>", "printSettings": "Configurações de impressão", "printGrayscale": "Imprimir escala de cinza", "printCurrentDisabled": "A visualização atual só está disponível ao visualizar uma única página."}, "printInfo": {"author": "Autor", "subject": "<PERSON><PERSON><PERSON>", "date": "Encontro"}, "redaction": {"markForRedaction": "Marcar para supressão"}, "searchPanel": {"caseSensitive": "Diferenciar maiúsculas e minúsculas", "wholeWordOnly": "Palavra inteira", "wildcard": "Curinga", "replace": "Substituir", "replaceAll": "Substitua tudo", "replaceText": "Substituir texto", "confirmMessageReplaceAll": "Tem certeza de que deseja substituir todo o texto?", "confirmMessageReplaceOne": "Tem certeza de que deseja substituir este texto?", "moreOptions": "<PERSON><PERSON>", "lessOptions": "Menos opções", "confirm": "confirme"}, "toolsOverlay": {"currentStamp": "Carimbo <PERSON>", "currentSignature": "Assinatura Atual", "signatureAltText": "Assinatura"}, "stampOverlay": {"addStamp": "Adicionar novo carimbo"}, "signatureOverlay": {"addSignature": "Adicionar assinatura", "addSignatureOrInitials": "Assinatura/Iniciais"}, "signatureModal": {"modalName": "Criar nova assinatura", "dragAndDrop": "Arraste e solte sua imagem aqui", "or": "Ou", "pickImage": "Escolher imagem da assinatura", "selectImage": "Selecione sua imagem aqui", "typeSignature": "Tipo de Assinatura*", "typeInitial": "Digite as iniciais*", "drawSignature": "<PERSON><PERSON><PERSON>*", "drawInitial": "Desenhe iniciais*", "imageSignature": "Assinatura de imagem", "imageInitial": "Inici<PERSON> da imagem", "pickInitialsFile": "Escolha as iniciais", "noSignatures": "No momento, não há assinaturas salvas.", "fontStyle": "est<PERSON> de fonte", "textSignature": {"dropdownLabel": "Família de <PERSON>"}}, "pageReplacementModal": {"yourFiles": "<PERSON>us arquivos", "chooseFile": "Escolha um arquivo", "localFile": "Arquivo local", "pageReplaceInputLabel": "Substitui<PERSON>(s)", "pageReplaceInputFromSource": "com página(s)", "warning": {"title": "Sair da página de substituição?", "message": "Sair cancel<PERSON><PERSON> todas as seleç<PERSON>es feitas até agora. Tem certeza de que ainda deseja sair?"}}, "filterAnnotModal": {"color": "Cor", "includeReplies": "Incluir Respostas", "filters": "<PERSON><PERSON><PERSON>", "user": "Do utilizador", "type": "<PERSON><PERSON>", "status": "Status", "filterSettings": "Configurações de filtro", "filterDocument": "Filtrar documento e painel de comentários"}, "state": {"accepted": "<PERSON><PERSON>", "rejected": "<PERSON><PERSON><PERSON><PERSON>", "completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cancelled": "Cancelado", "set": "Definir status:", "setBy": "definido por", "none": "<PERSON><PERSON><PERSON>", "marked": "Marcado", "unmarked": "<PERSON><PERSON> marcado"}, "measurementOverlay": {"scale": "Razão de escala", "angle": "<PERSON><PERSON><PERSON>", "distance": "Distância", "perimeter": "Perímetro", "area": "Á<PERSON>", "distanceMeasurement": "Medição de distância", "perimeterMeasurement": "Medição de perímetro", "arcMeasurement": "Medição de arco", "areaMeasurement": "Medição de área", "countMeasurement": "Medição de contagem", "radius": "Raio", "count": "Contagem", "length": "Comprimento", "xAxis": "Eixo X", "yAxis": "Eixo Y"}, "freeTextOption": {"autoSizeFont": "Dimensione o tamanho da fonte dinamicamente"}, "measurementOption": {"scale": "Escala", "selectScale": "Selecionar escala", "selectScaleDropdown": "Selecione Escala Suspensa"}, "measurement": {"scaleModal": {"calibrate": "Calibrar", "custom": "Personalizado", "fractionalUnits": "Unidades fracionárias", "precision": "Precisão", "preset": "Predefinição", "paperUnits": "Unidades de papel", "displayUnits": "Unidades de exibição", "fractionUnitsTooltip": "Unidades de fração aplicam-se apenas a in e ft-in", "incorrectSyntax": "Sintaxe incorreta", "units": "Unidades"}, "scaleOverlay": {"addNewScale": "Adicionar nova escala", "selectTwoPoints": "Selecione dois pontos de uma dimensão conhecida para calibrar", "inputKnowDimension": "Insira dimensões e unidades conhecidas para calibrar", "multipleScales": "Várias escalas"}, "deleteScaleModal": {"deleteScale": "Excluir escala", "scaleIsOn-delete-info": "Esta escala está atualmente em uso em", "page-delete-info": "p<PERSON><PERSON><PERSON>", "appliedTo-delete-info": "e aplicado a", "measurement": "medição", "measurements": "<PERSON><PERSON><PERSON>", "deletionIs": "A exclusão é", "irreversible": "irreversível", "willDeleteMeasurement": "e excluirá as medições associadas.", "confirmDelete": "Tem certeza de que deseja excluir esta escala?", "thisCantBeUndone": " <PERSON><PERSON> não pode ser desfeito.", "ifChangeScale": "Se você alterar a escala da medida ou ferramenta selecionada, a escala", "notUsedWillDelete": " não será mais usado por nenhuma medida ou ferramenta e será excluído. A exclusão é irreversível.", "ifToContinue": "Tem certeza de que deseja continuar?"}}, "contentEdit": {"deletionModal": {"title": "Excluir con<PERSON>", "message": "Tem certeza de que deseja excluir o conteúdo selecionado? Isso não pode ser desfeito."}, "digitalSign": {"title": "Aviso de documento selado", "message": "Este documento foi assinado e não pode ser alterado ou alterado."}}, "stylePopup": {"textStyle": "Estilo <PERSON>", "colors": "Cores", "invalidFontSize": "O tamanho da fonte deve estar no seguinte intervalo", "labelText": "Texto do rótulo", "labelTextPlaceholder": "Adicionar texto do rótulo"}, "styleOption": {"style": "<PERSON><PERSON><PERSON>", "solid": "<PERSON><PERSON><PERSON><PERSON>", "cloudy": "Nuvem"}, "slider": {"opacity": "Opacidade", "thickness": "E<PERSON><PERSON><PERSON>", "text": "Tamanho do texto"}, "shared": {"page": "<PERSON><PERSON><PERSON><PERSON>", "precision": "Precisão", "enableSnapping": "Ativar encaixe para ferramentas de medição"}, "watermark": {"title": "Marca d'á<PERSON>", "addWatermark": "Adicione uma Marca D'água", "size": "<PERSON><PERSON><PERSON>", "location": "Escolha um local para editar marcas d'água", "text": "Texto", "style": "<PERSON><PERSON><PERSON>", "resetAllSettings": "Voltar às configurações padrão", "font": "fonte", "addNew": "Adicionar novo", "locations": {"center": "Centro", "topLeft": "Cima Esquerda", "topRight": "Canto superior direito", "topCenter": "Centro Superior", "bottomLeft": "Inferior esquerdo", "bottomRight": "Canto inferior direito", "bottomCenter": "Centro Inferior"}}, "thumbnailPanel": {"delete": "Excluir", "rotateClockwise": "<PERSON><PERSON><PERSON>", "rotateCounterClockwise": "<PERSON><PERSON>do <PERSON>-<PERSON>", "rotatePageClockwise": "<PERSON><PERSON><PERSON> página no sentido horário", "rotatePageCounterClockwise": "G<PERSON>r página no sentido anti-horário", "moreOptions": "<PERSON><PERSON>", "moreOptionsMenu": "Menu Mais opções de miniaturas", "enterPageNumbers": "Insira os números das páginas para selecionar", "multiSelectPages": "Páginas de seleção múltipla", "multiSelectPagesExample": "por exemplo. 1, 3, 5-10"}, "thumbnailsControlOverlay": {"move": "Mover p<PERSON><PERSON><PERSON>"}, "richText": {"bold": "Negrito", "italic": "itálico", "underline": "<PERSON><PERSON><PERSON><PERSON>", "strikeout": "Riscado", "alignLeft": "Alinhamento de texto à esquerda", "alignRight": "Alinhamento de texto à direita", "alignCenter": "Alinhamento de texto no centro", "justifyCenter": "Centro de justificação de texto", "alignTop": "<PERSON><PERSON><PERSON> topo", "alignMiddle": "<PERSON><PERSON><PERSON> no meio", "alignBottom": "Alinhamento inferior"}, "customStampModal": {"modalName": "C<PERSON>r <PERSON>", "stampText": "Texto do carimbo", "timestampText": "Texto do carimbo de data / hora", "Username": "Nome do usuário", "Date": "Data", "Time": "<PERSON><PERSON>", "fontStyle": "est<PERSON> de fonte", "dateFormat": "Formato de data", "month": "<PERSON><PERSON><PERSON>", "day": "<PERSON>a", "year": "<PERSON><PERSON>", "hour": "<PERSON><PERSON>", "minute": "Min<PERSON>", "second": "<PERSON><PERSON><PERSON>", "textColor": "Cor do texto", "backgroundColor": "Cor de fundo", "dateToolTipLabel": "Mais informações sobre o formato de data", "previewCustomStamp": "Prévia <PERSON>"}, "pageRedactModal": {"addMark": "Adicionar marca", "pageSelection": "Seleção de página", "current": "<PERSON><PERSON><PERSON> atual", "specify": "Especificar páginas", "odd": "<PERSON>nte págin<PERSON>", "even": "Apenas páginas pares"}, "lineStyleOptions": {"title": "<PERSON><PERSON><PERSON>"}, "settings": {"settings": "Definições", "searchSettings": "Configurações de pesquisa", "general": "Em geral", "language": "Linguagem", "theme": "<PERSON><PERSON>", "darkMode": "<PERSON><PERSON> es<PERSON>ro", "lightMode": "<PERSON><PERSON> de luz", "advancedSetting": "Configuração avançada", "viewing": "Visualizando", "disableFadePageNavigationComponent": "Desativar o componente de navegação da página Fade", "disableFadePageNavigationComponentDesc": "Sempre mantenha o componente de navegação de página na tela. O comportamento padrão é esmaecê-lo após certo período de inatividade.", "disableNativeScrolling": "Desativar rolagem nativa", "disableNativeScrollingDesc": "Desative o comportamento de rolagem do dispositivo móvel nativo se ele tiver sido ativado anteriormente. Observe que o comportamento de rolagem do dispositivo móvel nativo está desativado por padrão.", "annotations": "Anotações", "disableToolDefaultStyleUpdateFromAnnotationPopup": "Desativar a atualização de estilo padrão da ferramenta do pop-up de anotação", "disableToolDefaultStyleUpdateFromAnnotationPopupDesc": "Desativa a sincronização de atualizações de estilo de anotação para a ferramenta associada que criou a anotação. Portanto, se o estilo de uma anotação for alterado, os estilos padrão da ferramenta não serão atualizados.", "notesPanel": "<PERSON><PERSON>as", "disableNoteSubmissionWithEnter": "Desabilitar o envio de notas com Enter", "disableNoteSubmissionWithEnterDesc": "Desative a capacidade de enviar notas pressionando Enter apenas se tiver sido ativado anteriormente. Isso reverterá o envio de notas para o padrão, que é Ctrl/Cmd + Enter.", "disableAutoExpandCommentThread": "Desativar tópico de comentários de expansão automática", "disableAutoExpandCommentThreadDesc": "Desativa a expansão automática de todos os tópicos de comentários no Painel de Notas.", "disableReplyCollapse": "Desativar Recolhimento de Resposta", "disableReplyCollapseDesc": "Desativa o recolhimento das respostas no Painel de Notas.", "disableTextCollapse": "Desativar recolhimento de texto", "disableTextCollapseDesc": "Desativa o recolhimento do texto da anotação no Painel de Notas.", "search": "Procurar", "disableClearSearchOnPanelClose": "Desabilitar Limpar P<PERSON>quisa no <PERSON>", "disableClearSearchOnPanelCloseDesc": "Desative a limpeza dos resultados da pesquisa quando o usuário fechar o painel de pesquisa. Quando desativado, os resultados da pesquisa são mantidos mesmo se o usuário fechar e reabrir o painel de pesquisa. Observe que os dispositivos móveis nunca limpam os resultados da pesquisa, mesmo que essa configuração esteja ativada. Isso ocorre porque o painel precisa ser fechado para visualizar os resultados da pesquisa no documento.", "pageManipulation": "Manipulação de página", "disablePageDeletionConfirmationModal": "Desativar modal de confirmação de exclusão de página", "disablePageDeletionConfirmationModalDesc": "Desative o modal de confirmação ao excluir uma página da visualização em miniatura", "disableMultiselect": "Desativar seleção múltipla", "disableMultiselectDesc": "Desative a seleção múltipla no painel de miniaturas esquerdo", "miscellaneous": "Diversos", "keyboardShortcut": "Atalho de teclado", "command": "Comand<PERSON>", "description": "Descrição", "action": "Ação", "rotateDocumentClockwise": "Girar o documento no sentido horário", "rotateDocumentCounterclockwise": "Gire o documento no sentido anti-horário", "copyText": "Copiar texto ou anotações selecionadas", "pasteText": "Colar texto ou anotações", "undoChange": "Desfazer uma alteração de anotação", "redoChange": "Refazer uma alteração de anotação", "openFile": "Abra o seletor de arquivos", "openSearch": "Abra a sobreposição de pesquisa", "zoomOptions": "Opções de zoom", "zoomIn": "<PERSON><PERSON>", "zoomOut": "Reduzir o zoom", "setHeaderFocus": "Define o foco para o cabeçalho", "fitScreenWidth": "Ajustar o documento à largura da tela em uma tela pequena (< 640px), caso contr<PERSON><PERSON>, ajustá-lo ao tamanho original", "print": "Imprimir", "bookmarkOpenPanel": "Marque rapidamente uma página e abra o painel de favoritos", "goToPreviousPage": "Ir para a página anterior", "goToNextPage": "Vá para a página seguinte", "goToPreviousPageArrowUp": "Ir para a página anterior no modo de layout único (ArrowUp)", "goToNextPageArrowDown": "Vá para a próxima página no modo de layout único (ArrowDown)", "holdSwitchPan": "Segure para alternar para o modo Pan e solte para retornar à ferramenta anterior", "selectAnnotationEdit": "Selecione a ferramenta AnnotationEdit", "selectPan": "Selecione a ferramenta Panorâmica", "selectCreateArrowTool": "Selecione a ferramenta AnnotationCreateArrow", "selectCreateCalloutTool": "Selecione a ferramenta AnnotationCreateCallout", "selectEraserTool": "Selecione a ferramenta AnnotationEraser", "selectCreateFreeHandTool": "Selecione a ferramenta AnnotationCreateFreeHand", "selectCreateStampTool": "Selecione a ferramenta AnnotationCreateStamp", "selectCreateLineTool": "Selecione a ferramenta AnnotationCreateLine", "selectCreateStickyTool": "Selecione a ferramenta AnnotationCreateSticky", "selectCreateEllipseTool": "Selecione a ferramenta AnnotationCreateEllipse", "selectCreateRectangleTool": "Selecione a ferramenta AnnotationCreateRectangle", "selectCreateRubberStampTool": "Selecione a ferramenta AnnotationCreateRubberStamp", "selectCreateFreeTextTool": "Selecione a ferramenta AnnotationCreateFreeText", "openSignatureModal": "Abra o modal de assinatura ou a sobreposição", "selectCreateTextSquigglyTool": "Selecione a ferramenta AnnotationCreateTextSquiggly", "selectCreateTextHighlightTool": "Selecione a ferramenta AnnotationCreateTextHighlight", "selectCreateTextStrikeoutTool": "Selecione a ferramenta AnnotationCreateTextStrikeout", "selectCreateTextUnderlineTool": "Selecione a ferramenta AnnotationCreateTextUnderline", "editKeyboardShorcut": "Editar atalho de teclado", "setShortcut": "<PERSON><PERSON><PERSON> at<PERSON><PERSON>", "editShortcut": "<PERSON><PERSON>", "shortcutAlreadyExists": "O atalho de teclado acima já existe.", "close": "<PERSON><PERSON>r dica"}}, "warning": {"deletePage": {"deleteTitle": "Excluir p<PERSON>", "deleteMessage": "Tem certeza de que deseja excluir as páginas selecionadas? Isso não pode ser desfeito.", "deleteLastPageMessage": "Você não pode excluir todas as páginas do documento."}, "extractPage": {"title": "Extrair página", "message": "Tem certeza de que deseja extrair as páginas selecionadas?", "confirmBtn": "Extrair páginas", "secondaryBtn": "Extrair e remover página (s)"}, "redaction": {"applyTile": "Aplicar supressão", "applyMessage": "Esta ação suprimirá permanentemente todos os itens selecionados para remoção. Isso não pode ser desfeito."}, "deleteBookmark": {"title": "Excluir marcador?", "message": "Tem certeza de que deseja excluir esses favoritos? Você não pode desfazer esta ação."}, "deleteOutline": {"title": "Excluir esboço?", "message": "Tem certeza de que deseja excluir os marcadores selecionados?\n\nUm marcador que tenha marcadores aninhados resultará na exclusão de toda a estrutura e precisarão ser adicionados novamente caso necessário."}, "selectPage": {"selectTitle": "Nenhuma página selecionada", "selectMessage": "<PERSON><PERSON><PERSON><PERSON> as páginas e tente novamente."}, "colorPicker": {"deleteTitle": "Excluir cor personalizada", "deleteMessage": "Excluir a cor personalizada selecionada? Ele será removido da sua paleta de cores."}, "colorPalettePicker": {"deleteTitle": "Excluir cor personalizada"}, "multiDeleteAnnotation": {"title": "Excluir anotações?", "message": "A exclusão removerá todos os comentários, respostas e agrupamentos e não poderá ser desfeita.\n\n Tem certeza de que deseja excluir essas anotações?"}, "closeFile": {"title": "<PERSON><PERSON><PERSON> sem baixar?", "message": "Foram feitas alterações neste documento. Tem certeza de que deseja fechá-lo sem baixar seu trabalho? Você não pode desfazer esta ação.", "rejectDownloadButton": "<PERSON><PERSON><PERSON> sem baixar"}, "connectToURL": {"title": "Aviso de segurança", "message": "Este documento está tentando se conectar a:\n\n{{- uri}}\n\n Se você confia neste documento, clique em Confirmar para abri-lo."}, "sheetTabRenameIssueOne": {"title": "Aviso de edição", "message": "Este nome de planilha já existe. Por favor, insira outro nome."}, "sheetTabRenameIssueTwo": {"title": "Aviso de edição", "message": "Este nome de planilha não pode estar vazio."}, "sheetTabDeleteMessage": {"title": "Aviso de edição", "message": "Tem certeza de que deseja excluir esta planilha?"}}, "shortcut": {"arrow": "(A)", "callout": "(C)", "copy": "(Ctrl C)", "delete": "(Del)", "ellipse": "(O)", "eraser": "(E)", "freehand": "(F)", "freetext": "(T)", "highlight": "(H)", "line": "(L)", "pan": "(P)", "rectangle": "(R)", "rotateClockwise": "(Ctrl Shift +)", "rotateCounterClockwise": "(Ctrl Shift -)", "select": "(Esc)", "signature": "(S)", "squiggly": "(G)", "image": "(I)", "redo": "(Ctrl Shift Z)", "redo_windows": "(Ctrl Y)", "undo": "(Ctrl Z)", "stickyNote": "(N)", "strikeout": "(K)", "underline": "(U)", "zoomIn": "(Ctrl +)", "zoomOut": "(Ctrl -)", "richText": {"bold": "(Ctrl B)", "italic": "(Ctrl I)", "underline": "(Ctrl U)", "strikeout": "(Ctrl K)"}, "rotate3D": "Shift + arrastar", "zoom3D": "Shift + Scroll"}, "tool": {"pan": "Mover", "select": "Selecionar", "selectAnOption": "Selecione uma opção", "Marquee": "Zoom na seleção", "Link": "URL ou página do link", "Standard": "Padrão", "Custom": "personalizadas"}, "link": {"url": "URL", "page": "<PERSON><PERSON><PERSON><PERSON>", "enterurl": "Digite a URL que você deseja vincular", "enterUrlAlt": "Insira o URL", "insertLink": "Inserir link", "insertLinkOrPage": "<PERSON><PERSON><PERSON> link ou página", "enterpage": "Digite o número da página que você deseja vincular", "urlLink": "URL"}, "Model3D": {"add3D": "Adicione uma anotação 3D inserindo URL", "enterurl": "Insira o URL para o objeto 3D no formato glTF", "enterurlOrLocalFile": "Insira o URL ou carregue um objeto 3D no formato glTF", "formatError": "Apenas o formato glTF (.glb) é compatível"}, "OpenFile": {"enterUrlOrChooseFile": "Insira um URL ou escolha um arquivo para carregar no WebViewer", "enterUrl": "Digite o URL do arquivo", "extension": "Extensão de arquivo", "existingFile": "O arquivo já está aberto", "addTab": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>", "newTab": "Nova aba"}, "datePicker": {"previousMonth": "M<PERSON>s anterior", "nextMonth": "<PERSON>r<PERSON><PERSON><PERSON>", "months": {"0": "Janeiro", "1": "<PERSON><PERSON>", "2": "Março", "3": "Abril", "4": "<PERSON><PERSON>", "5": "<PERSON><PERSON>", "6": "<PERSON><PERSON>", "7": "Agosto", "8": "Setembro", "9": "Out<PERSON>ro", "10": "Novembro", "11": "Dezembro"}, "monthsShort": {"0": "Jan", "1": "<PERSON>v", "2": "Mar", "3": "Abr", "4": "<PERSON>", "5": "Jun", "6": "Jul", "7": "Ago", "8": "Set", "9": "Out", "10": "Nov", "11": "<PERSON>z"}, "weekdays": {"0": "Domingo", "1": "Segunda-feira", "2": "Terça-feira", "3": "Quarta-feira", "4": "Quin<PERSON>-f<PERSON>", "5": "Sexta-feira", "6": "Sábado"}, "weekdaysShort": {"0": "Dom", "1": "Seg", "2": "<PERSON><PERSON>", "3": "<PERSON>ua", "4": "<PERSON>ui", "5": "Sex", "6": "<PERSON><PERSON><PERSON>"}, "today": "Hoje", "invalidDateTime": "Data/hora inválida: a entrada deve corresponder ao formato"}, "formField": {"indexPanel": {"formFieldList": "Lista de campos do formulário", "notFields": "Este documento não possui campos de formulário"}, "formFieldPopup": {"fieldName": "Nome do Campo", "fieldValue": "<PERSON>or <PERSON>", "readOnly": "<PERSON>nte leitura", "multiSelect": "Multi Select", "required": "Obrigatório", "multiLine": "Multilinha", "apply": "Aplicar", "cancel": "<PERSON><PERSON><PERSON>", "flags": "Bandeiras de campo", "fieldSize": "Tamanho do campo", "options": "Opções", "properties": "<PERSON><PERSON><PERSON><PERSON>", "radioGroups": "Os botões de opção com o mesmo nome de campo pertencerão ao mesmo agrupamento.", "nameRequired": "O nome do campo é obrigatório", "fieldIndicator": "Indicadores de campo", "documentFieldIndicator": "Indicadores de campo do documento", "includeFieldIndicator": "Incluir indicador de campo", "indicatorPlaceHolders": {"TextFormField": "Inserir texto aqui", "CheckBoxFormField": "Verificar", "RadioButtonFormField": "Encher", "ListBoxFormField": "Selecionar", "ComboBoxFormField": "Selecionar", "SignatureFormField": {"fullSignature": "Assine aqui", "initialsSignature": "Inicial aqui"}}, "size": "<PERSON><PERSON><PERSON>", "width": "<PERSON><PERSON><PERSON>", "height": "Altura", "invalidField": {"duplicate": "Nome do campo já existe", "empty": "O nome do campo não pode estar vazio"}}, "formFieldPanel": {"SignatureFormField": "Anotação de campo de assinatura", "CheckBoxFormField": "Anotação de campo de caixa de seleção", "RadioButtonFormField": "Anotação de campo de botão de opção", "ListBoxFormField": "Anotação de campo de caixa de listagem", "ComboBoxFormField": "Anotação de campo de caixa de combinação", "TextFormField": "Anotação de campo de texto"}, "apply": "Aplicar Campos", "type": "Tipo de campo", "types": {"text": "Texto", "signature": "Assinatura", "checkbox": "Caixa de seleção", "radio": "Botao de radio", "listbox": "Caixa de lista", "combobox": "Caixa combo", "button": "Botão"}}, "alignmentPopup": {"alignment": "<PERSON><PERSON><PERSON>", "alignLeft": "Alinhar à esquerda", "alignHorizontalCenter": "Alinhar centro horizontal", "alignVerticalCenter": "Alinhar centro vertical", "alignRight": "Alinhar à direita", "alignTop": "<PERSON><PERSON><PERSON>", "alignBottom": "Alinhar parte inferior", "distribute": "Distribuir", "distributeHorizontal": "Distribuir horizontalmente", "distributeVertical": "Distribuir verticalmente"}, "digitalSignatureModal": {"certification": "certificação", "Certification": "Certificação", "signature": "assinatura", "Signature": "Assinatura", "valid": "v<PERSON><PERSON><PERSON>", "invalid": "<PERSON>v<PERSON><PERSON><PERSON>", "unknown": "desconhecido", "title": "Propriedades de {{type}}", "header": {"documentIntegrity": "Integridade do Documento", "identitiesTrust": "Identidades e confiança", "generalErrors": "<PERSON><PERSON><PERSON>", "digestStatus": "Status de resumo"}, "documentPermission": {"noChangesAllowed": "O {{editor}} especificou que nenhuma alteração é permitida para este documento", "formfillingSigningAllowed": "O {{editor}} especificou que o preenchimento de formulário e assinatura são permitidos para este documento. Nenhuma outra alteração é permitida.", "annotatingFormfillingSigningAllowed": "O {{editor}} especificou que o preenchimento de formulários, assinatura e comentários são permitidos para este documento. Nenhuma outra alteração é permitida.", "unrestricted": "O {{editor}} especificou que não há restrições para este documento."}, "digestAlgorithm": {"preamble": "O algoritmo de resumo usado para assinar a assinatura:", "unknown": "O algoritmo de resumo usado para assinar a assinatura é desconhecido."}, "trustVerification": {"none": "Nenhum resultado de verificação de confiança detalhado disponível.", "current": "Tentativa de verificação de confiança em relação ao horário atual", "signing": "Tentativa de verificação de confiança em relação ao tempo de assinatura: {{trustVerificationTime}}", "timestamp": "Tentativa de verificação de confiança em relação ao carimbo de data / hora incorporado seguro: {{trustVerificationTime}}"}, "signerIdentity": {"preamble": "A identidade do signatário é", "valid": "válido.", "unknown": "desconhecido."}, "summaryBox": {"summary": "Digital {{type}} é {{status}}", "signedBy": ", assinado por {{name}}"}}, "digitalSignatureVerification": {"certifier": "certificar", "certified": "certificado", "Certified": "Certificado", "Certification": "Certificação", "signer": "placa", "signed": "assinado", "Signed": "<PERSON><PERSON><PERSON>", "Signature": "Assinatura", "by": "de", "on": "em", "disallowedChange": "Alteração não permitida: {{type}}, objnum: {{objnum}}", "unsignedSignatureField": "Campo de assinatura não assinado: {{fieldName}}", "signatureProperties": "Propriedades da assinatura", "trustVerification": {"current": "A hora de verificação usada foi a hora atual", "signing": "O tempo de verificação é a partir do relógio no computador do signatário", "timestamp": "O tempo de verificação é do carimbo de data / hora seguro incorporado no documento", "verifiedTrust": "Resultado da verificação de confiança: Verificado", "noTrustVerification": "Nenhum resultado de verificação de confiança detalhado disponível."}, "permissionStatus": {"noPermissionsStatus": "Nenhum status de permissão para relatar.", "permissionsVerificationDisabled": "A verificação de permissões foi desativada.", "hasAllowedChanges": "O documento tem alterações que são permitidas pelas configurações de permissões de assinaturas.", "invalidatedByDisallowedChanges": "O documento tem alterações que não são permitidas pelas configurações de permissões de assinaturas.", "unmodified": "O documento não foi modificado desde que foi", "unsupportedPermissionsFeatures": "Foram encontrados recursos de permissões não suportados."}, "trustStatus": {"trustVerified": "Confiança estabelecida em {{verificationType}} com sucesso.", "untrusted": "A confiança não pôde ser estabelecida.", "trustVerificationDisabled": "A verificação de confiança foi desativada.", "noTrustStatus": "Nenhum status de confiança para relatar."}, "digestStatus": {"digestInvalid": "O resumo está incorreto.", "digestVerified": "O resumo está correto.", "digestVerificationDisabled": "A verificação resumida foi desativada.", "weakDigestAlgorithmButDigestVerifiable": "O resumo está correto, mas o algoritmo de resumo é fraco e inseguro.", "noDigestStatus": "Nenhum status de resumo para relatar.", "unsupportedEncoding": "Nenhum SignatureHandler instalado foi capaz de reconhecer a codificação da assinatura", "documentHasBeenAltered": "O documento foi alterado ou corrompido desde que foi assinado."}, "documentStatus": {"noError": "Nenhum erro geral para relatar.", "corruptFile": "SignatureHandler relatou corrupção de arquivo.", "unsigned": "A assinatura ainda não foi assinada criptograficamente.", "badByteRanges": "SignatureHandler relata corrupção em ByteRanges na assinatura digital.", "corruptCryptographicContents": "SignatureHandler relata corrupção no conteúdo da assinatura digital."}, "signatureDetails": {"signatureDetails": "Detalhes de assinatura", "contactInformation": "Informações de Contato", "location": "Localização", "reason": "Razão", "signingTime": "Hora de Assinatura", "noContactInformation": "Nenhuma informação de contato fornecida", "noLocation": "Nenhum local fornecido", "noReason": "Nenhum motivo fornecido", "noSigningTime": "Nenhuma hora de assinatura encontrada"}, "panelMessages": {"noSignatureFields": "Este documento não possui campos de assinatura", "certificateDownloadError": "Erro encontrado ao tentar baixar um certificado confiável", "localCertificateError": "Existem alguns problemas com a leitura de um certificado local"}, "verificationStatus": {"valid": "{{verificationType}} <PERSON> válido.", "failed": "{{verificationType}} falhou."}}, "cropPopUp": {"title": "Páginas para cortar", "allPages": "Todo", "singlePage": "<PERSON><PERSON><PERSON> atual", "multiPage": "Especificar página", "cropDimensions": "Dimensões de corte", "dimensionInput": {"unitOfMeasurement": "Unidade", "autoTrim": "Corte automático", "autoTrimCustom": "Personalizado"}, "cropModal": {"applyTitle": "Aplicar colheita?", "applyMessage": "Esta ação cortará permanentemente todas as páginas selecionadas selecionadas. Não pode ser desfeito.", "cancelTitle": "<PERSON>celar col<PERSON><PERSON>?", "cancelMessage": "Tem certeza de que deseja cancelar o corte de todas as páginas selecionadas?"}}, "snippingPopUp": {"title": "Ferramenta de Recorte", "clipboard": "Copiar para a Área de Transferência", "download": "Baixar", "cropAndRemove": "Recortar e Remover", "snippingModal": {"applyTitle": "Aplicar recorte?", "applyMessage": "Esta ação recortará permanentemente a área especificada e removerá outras páginas. Não pode ser desfeito.", "cancelTitle": "Cancelar recorte?", "cancelMessage": "Tem certeza de que deseja interromper o recorte?"}}, "textEditingPanel": {"paragraph": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "text": "Texto"}, "redactionPanel": {"noMarkedRedactions": "Comece a redigir marcando texto, regi<PERSON><PERSON>, páginas ou fazendo uma pesquisa.", "redactionSearchPlaceholder": "Editar por teclado ou padrões", "redactionCounter": "Marcado para redação", "clearMarked": "<PERSON><PERSON><PERSON>", "redactAllMarked": "<PERSON><PERSON>r tudo", "redactionItems": "Itens de Redação", "redactionItem": {"regionRedaction": "Redação da região", "fullPageRedaction": "Redação de página inteira", "fullVideoFrameRedaction": "Redação de vídeo", "audioRedaction": "Redação de áudio", "fullVideoFrameAndAudioRedaction": "Redação de vídeo e áudio", "image": "Imagem"}, "expand": "Expandir", "collapse": "Colapso", "searchResults": "<PERSON><PERSON><PERSON>", "search": {"creditCards": "Cartão de crédito", "phoneNumbers": "Números de telefone", "images": "Imagens", "emails": "E-mails", "pattern": "<PERSON><PERSON><PERSON><PERSON>", "start": "Comece a fazer sua pesquisa"}}, "wv3dPropertiesPanel": {"propertiesHeader": "<PERSON><PERSON><PERSON><PERSON>", "miscValuesHeader": "Todos", "emptyPanelMessage": "Selecione um elemento para ver suas propriedades."}, "watermarkPanel": {"textWatermark": "Marca d'água de texto", "uploadImage": "Enviar Imagem", "browse": "Navegar", "watermarkOptions": "Opções de marca d'água", "watermarks": "Marcas d'água"}, "portfolio": {"createPDFPortfolio": "Criar Portfólio PDF", "uploadFiles": "Fazer upload de arquivos", "uploadFolder": "<PERSON>eg<PERSON>", "addFiles": "Adicionar <PERSON>", "addFile": "<PERSON><PERSON><PERSON><PERSON>", "addFolder": "Adicionar pasta", "createFolder": "Criar pasta", "portfolioPanelTitle": "Portfólio PDF", "portfolioNewFolder": "Nova pasta", "portfolioDocumentTitle": "Título do documento", "portfolioFolderPlaceholder": "Nome da pasta", "portfolioFilePlaceholder": "Nome do arquivo", "folderNameAlreadyExists": "O nome da pasta já existe", "fileNameAlreadyExists": "O nome do arquivo já existe", "openFile": "Abrir em nova guia", "fileAlreadyExists": "O arquivo já existe", "fileAlreadyExistsMessage": "O arquivo \"{{fileName}}\" já existe no portfólio.", "deletePortfolio": "Tem certeza de que deseja excluir \"{{fileName}}\"?", "reselect": "Selecione novamente"}, "languageModal": {"selectLanguage": "Selecione o idioma"}, "officeEditor": {"bold": "<PERSON><PERSON><PERSON><PERSON>", "italic": "itálico", "underline": "<PERSON><PERSON><PERSON><PERSON>", "textColor": "Cor do texto", "leftAlign": "<PERSON><PERSON><PERSON> à esquerda", "centerAlign": "Alinhar ao centro", "rightAlign": "alinhar à direita", "justify": "Justificar", "lineSpacing": "Espaçamento entre linhas e parágrafos", "lineSpacingMenu": "Espaçamento entre linhas", "bulletList": "Lista com marcadores", "numberList": "Lista numerada", "decreaseIndent": "<PERSON><PERSON><PERSON> de<PERSON>", "increaseIndent": "Aumentar recuo", "nonPrintingCharacters": "Caracteres não imprimíveis", "insertLink": "Inserir link", "insertImage": "Inserir Imagem", "image": "Imagem", "table": "Mesa", "insertRowAbove": "<PERSON><PERSON><PERSON> linha acima", "insertRowBelow": "<PERSON>ser<PERSON> linha a<PERSON>o", "insertColumnRight": "Inserir coluna à direita", "insertColumnLeft": "Inserir coluna à esquerda", "deleteRow": "Excluir linha", "deleteColumn": "Excluir coluna", "deleteTable": "Excluir tabela", "deleted": "Excluído:", "added": "Adicionado:", "editing": "Edição", "editingDescription": "Editar documento", "reviewing": "Revendo", "reviewingDescription": "Dar sugestõ<PERSON>", "viewOnly": "Ver somente", "viewOnlyDescription": "Ver sem sugestões", "notSupportedOnMobile": "A edição do Office não é suportada em dispositivos móveis.", "previewAllChanges": "<PERSON><PERSON><PERSON> to<PERSON> as altera<PERSON><PERSON><PERSON>", "accept": "Aceitar", "reject": "<PERSON><PERSON><PERSON><PERSON>", "pastingTitle": "Colagem não disponível", "pastingMessage": "Colar não é compatível com seu navegador. Em vez disso, você pode usar um atalho de teclado", "pastingWithoutFormatTitle": "Colar sem formatação não disponível", "pastingWithoutFormatMessage": "Colar sem formatação não é compatível com seu navegador. Em vez disso, você pode usar um atalho de teclado", "breaks": "Pausas", "pageBreak": "Quebra de página", "pageBreakDescription": "Fim da página e início de uma nova página", "sectionBreakNextPage": "Quebra de Seção - Próxima Página", "sectionBreakNextPageDescription": "Inserir quebra de seção e começar na próxima página", "sectionBreakContinuous": "Quebra de Seção - Contínua", "sectionBreakContinuousDescription": "Insira uma quebra de seção e continue na mesma página", "section": "Seção", "header": {"0": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "1": "Cabeçalho da primeira página", "2": "Cabeçalho de página par", "3": "Cabeçalho de página ímpar", "-1": "Cabeçalho <PERSON>"}, "footer": {"0": "Rodapé", "1": "Rodap<PERSON> da primeira página", "2": "Rodapé de página par", "3": "Rodapé de página ímpar", "-1": "<PERSON><PERSON><PERSON>"}, "options": "Opções", "pageOptions": "Opções de página", "removeHeader": "Remover <PERSON>", "removeFooter": "Remover rodapé", "headerFooterOptionsModal": {"title": "Formato de cabeçalho e rodapé", "margins": "Margens", "headerFromTop": "Cabeçalho do topo", "footerFromBottom": "<PERSON><PERSON><PERSON>ai<PERSON>", "layouts": {"layout": "Disposição", "noSelection": "<PERSON><PERSON><PERSON><PERSON>", "differentFirstPage": "Primeira página diferente", "differentEvenOddPages": "Diferentes páginas pares e ímpares", "differentFirstEvenOddPages": "Primeiras páginas diferentes, pares e ímpares"}}, "lineSpacingOptions": {"15": "1.5", "115": "1.15", "single": "<PERSON><PERSON>iro", "double": "Dobro", "custom": "Personalizado"}, "numberDropdown": {"6": "Número latino romano 1", "7": "Número <PERSON>", "8": "Número latino romano 2", "10": "<PERSON><PERSON><PERSON>", "11": "Número latino romano", "dropdownLabel": "Predefinições de numeração"}, "bulletDropdown": {"0": "Bala", "1": "Quadrado de bala", "2": "Bala quadrada", "3": "Diamante", "4": "Verificar", "5": "<PERSON><PERSON>", "dropdownLabel": "Predefinições de marcadores"}, "fontSize": {"dropdownLabel": "<PERSON><PERSON><PERSON>"}, "fontStyles": {"dropdownLabel": "Estilos de fonte"}, "fontFamily": {"dropdownLabel": "Família de <PERSON>"}}, "spreadsheetEditor": {"editing": "Edição", "viewOnly": "Visualização", "editingDescription": "Editar documento", "viewOnlyDescription": "Somente visualização", "bold": "<PERSON><PERSON><PERSON><PERSON>", "italic": "itálico", "underline": "<PERSON><PERSON><PERSON><PERSON>", "strikethrough": "<PERSON><PERSON><PERSON>", "cellBorderStyle": "<PERSON><PERSON><PERSON>", "merge": "Mesclar", "unmerge": "<PERSON><PERSON><PERSON> me<PERSON>lage<PERSON>", "cellFormat": "Formato de célula", "automatic": "Automático", "plainText": "Texto simples", "increaseDecimal": "Aumentar decimal", "decreaseDecimal": "Diminuir decimal", "number": "Número", "percent": "Por cento", "accounting": "Contabilidade", "financial": "Financeiro", "currency": "<PERSON><PERSON>", "currencyRounded": "<PERSON><PERSON> a<PERSON>", "calendar": "Data", "clockHour": "Tempo", "calendarTime": "Data e hora", "formatAsCurrency": "Formatar como moeda", "formatAsPercent": "Formato como porcentagem", "formatAsDecDecimal": "Diminuir ponto decimal", "formatAsIncDecimal": "Aumentar ponto decimal", "fontColor": "<PERSON><PERSON> da fonte", "cellBackgroundColor": "Cor de fundo da célula", "textAlignment": "Alinhamento de texto", "alignLeft": "Alinhar à esquerda", "alignCenter": "Alinhar centro", "alignRight": "Alinhar à direita", "alignTop": "<PERSON><PERSON><PERSON> topo", "alignMiddle": "<PERSON><PERSON><PERSON> no meio", "alignBottom": "<PERSON><PERSON><PERSON> a parte inferior", "cellAdjustment": "Ajuste de <PERSON>lula", "insertColLeft": "Inserir coluna à esquerda", "columnInsertLeft": "Inserir coluna à esquerda", "columnInsertRight": "Inserir coluna à direita", "rowInsertTop": "<PERSON><PERSON><PERSON> linha acima", "rowInsertBottom": "<PERSON>ser<PERSON> linha a<PERSON>o", "columnInsertShiftDown": "Inserir células e deslocar para baixo", "columnInsertShiftRight": "Inserir células e deslocar para a direita", "columnDelete": "Excluir coluna", "rowDelete": "Excluir linha", "columnDeleteShiftUp": "Excluir células e deslocar para cima", "columnDeleteShiftLeft": "Excluir células e deslocar para a esquerda"}, "insertPageModal": {"title": "Inserir nova página", "tabs": {"blank": "<PERSON> branco", "upload": "<PERSON><PERSON>"}, "pagePlacements": {"header": "Posicionamento da página", "above": "Acima da página", "below": "Abaixo da página"}, "pageLocations": {"header": "Localização da página", "specify": "Especificar página", "specifyLocation": "Especifique o local da página", "amount": "Quantidade de páginas", "total": "Total", "pages": "<PERSON><PERSON><PERSON><PERSON>"}, "pageDimensions": {"header": "Dimensões da página", "subHeader": "Predefinições", "presets": {"letter": "Carta", "halfLetter": "<PERSON><PERSON> letra", "juniorLegal": "Jurídico Júnior", "custom": "Personalizado"}, "units": "Unidades"}, "browse": "<PERSON><PERSON><PERSON>", "fileSelected": {"title": "<PERSON><PERSON><PERSON><PERSON> as páginas a serem adicionadas"}, "button": "<PERSON><PERSON><PERSON><PERSON>(s)", "selectPages": "<PERSON><PERSON><PERSON><PERSON> as páginas a serem adicionadas", "page": "<PERSON><PERSON><PERSON><PERSON>", "warning": {"title": "Sair inserir nova página?", "message": "Sair cancel<PERSON><PERSON> todas as seleç<PERSON>es feitas até agora. Tem certeza de que ainda deseja sair?"}}, "multiViewer": {"dragAndDrop": "Arraste e solte seu arquivo aqui para comparar", "or": "Ou", "browse": "<PERSON><PERSON><PERSON>", "startSync": "Iniciar <PERSON>cron<PERSON>", "stopSync": "Parar sincroniza<PERSON>", "closeDocument": "<PERSON><PERSON><PERSON> documento", "save": "<PERSON><PERSON> documento", "comparePanel": {"Find": "Localizar no documento", "changesList": "Lista de alterações", "change": "<PERSON><PERSON>", "old": "<PERSON><PERSON><PERSON>", "new": "Novo", "page": "<PERSON><PERSON><PERSON><PERSON>", "textContent": "conte<PERSON>do de texto", "delete": "excluir", "insert": "inserir", "edit": "editar"}}, "saveModal": {"close": "<PERSON><PERSON>", "saveAs": "<PERSON><PERSON> como", "general": "Em geral", "fileName": "Nome do arquivo", "fileType": "Tipo de arquivo", "fileLocation": "Localização de arquivo", "browse": "Navegar", "pageRange": "Intervalo de páginas", "all": "<PERSON><PERSON>", "currentView": "<PERSON><PERSON><PERSON>", "currentPage": "<PERSON><PERSON><PERSON> atual", "specifyPage": "Especificar página", "properties": "<PERSON><PERSON><PERSON><PERSON>", "includeAnnotation": "Incluir anotações", "includeComments": "<PERSON>luir coment<PERSON>", "watermark": "Marca d'á<PERSON>", "addWatermark": "Adicione uma Marca D'água", "save": "<PERSON><PERSON>qui<PERSON>", "pageError": "Insira um intervalo entre 1 - ", "fileNameCannotBeEmpty": "O nome do arquivo não pode ficar vazio"}, "filePicker": {"dragAndDrop": "Arraste e solte seu arquivo aqui", "selectFile": "Selecione seu arquivo aqui", "or": "Ou"}, "stylePanel": {"headings": {"styles": "Estilos", "annotation": "Anotação", "annotations": "Anotações", "tool": "Ferramenta", "textStyles": "Estilos de texto", "currentColor": "<PERSON>r atual", "customColors": "<PERSON>s personalizadas", "redactionTextLabel": "Etiqueta de texto", "redactionMarkOutline": "<PERSON><PERSON> con<PERSON>", "redactionFill": "Cor de redação", "redactionTextPlaceholder": "Inserir ró<PERSON>lo de texto", "contentEdit": "Edição de conteúdo"}, "lineStyles": {"startLineStyleLabel": "<PERSON>st<PERSON> da linha de partida", "middleLineStyleLabel": "<PERSON><PERSON><PERSON> da l<PERSON>ha média", "endLineStyleLabel": "<PERSON><PERSON><PERSON> l<PERSON> final"}, "addColorToCustom": "Adicionar às cores personalizadas", "noToolSelected": "Selecione uma ferramenta para visualizar as propriedades da ferramenta", "noToolStyle": "A ferramenta não contém nenhuma propriedade de estilo.", "lineEnding": {"start": {"dropdownLabel": "<PERSON><PERSON><PERSON> l<PERSON>"}, "end": {"dropdownLabel": "<PERSON><PERSON> da linha"}, "middle": {"dropdownLabel": "Linha do meio"}}, "borderStyle": {"dropdownLabel": "Fronteira"}}, "signatureListPanel": {"header": "Lista de assinaturas", "newSignature": "Nova Assinatura", "newSignatureAndInitial": "Nova Assinatura e Inicial", "signatureList": {"signature": "Assinatura", "initials": "Iniciais"}}, "rubberStampPanel": {"header": "<PERSON><PERSON>", "standard": "<PERSON><PERSON>"}, "colorPickerModal": {"modalTitle": "<PERSON><PERSON>or de cores"}, "accessibility": {"landmarks": {"topHeader": "Cabeçalho superior", "leftHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rightHeader": "Cabe<PERSON><PERSON><PERSON>", "bottomHeader": "Cabeçalho inferior", "documentContent": "Conteúdo do documento"}, "label": "Acessibilidade", "accessibilityMode": "Modo de acessibilidade", "skipTo": "Pular para", "document": "Documento", "notes": "Notas"}, "formulaBar": {"label": "Barra de Fórmula", "range": "Faixa", "formulas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sumif": "Uma soma condicional em um intervalo", "sumsq": "Retorna a soma dos quadrados de um intervalo", "sum": "Adiciona todos os números em um intervalo", "asinh": "Retorna o seno hiperbólico inverso de um número", "acos": "Retorna o arco cosseno de um número, em radianos", "cosh": "Retorna o cosseno hiperbólico de um número", "iseven": "Verifica se um número é par", "isodd": "Verifica se um número é ímpar"}}