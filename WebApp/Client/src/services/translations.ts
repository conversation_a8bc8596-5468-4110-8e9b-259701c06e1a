type TranslationType = {
	Components: Record<string, any>
	ScriptMessages: Record<string, any>
}

class TranslationService {

	translate(key: string, alt?: string) {
		if (typeof window.Translations !== 'object' || typeof window.Translations.ScriptMessages !== 'object')
			return alt

		return window.Translations.ScriptMessages[key] ?? alt
	}
}

declare global {
	interface Window {
		Translations: TranslationType
		I18n: TranslationService
	}
}

const I18n = new TranslationService()
window.I18n = I18n
export default I18n