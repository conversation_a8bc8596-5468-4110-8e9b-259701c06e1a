Token Generation Instructions

  Token generation is NOT done on the embedded device. Tokens (keycodes) are generated by external backend systems and sent to devices for validation.

  Account Details Setup for Units

  Required Account Information:

  1. Serial ID: Unique integer identifier (user-facing)
  2. Secret Key: 16-byte hexadecimal authentication key
  3. Nexus ID: Authority ID + Device ID combination
  4. Channel Secret Key: 16-byte key for channel communication

  Implementation Steps:

  1. Factory Provisioning - Set these in product_nexus_identity.c:
  product_nexus_identity_set_nexus_id(&device_id);
  product_nexus_identity_set_nexus_keycode_secret_key(&secret_key);
  product_nexus_identity_set_nexus_channel_secret_key(&channel_key);
  2. Implement Platform Functions in your code:
  uint32_t nxp_keycode_get_user_facing_id(void);  // Return serial ID
  struct nx_common_check_key nxp_keycode_get_secret_key(void);  // Return secret key
  3. Configuration: Run python conf_nexus.py from nexus directory to configure protocol options

  Example Account Setup:

  - Serial ID: ********
  - Secret Key: DEADBEEF1020304004030201FEEBDAED
  - Authority ID: 0x0000
  - Device ID: Unique per device

  For Token Generation:

  Contact Angaza for backend services or APIs to generate keycodes. The embedded library validates tokens but doesn't create them.
